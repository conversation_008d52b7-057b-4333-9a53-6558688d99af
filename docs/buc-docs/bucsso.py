#!/usr/bin/env python
# -*- coding: utf-8 -*-

'''
Buc SSO wiki
http://gitlab.alibaba-inc.com/buc/sso/wikis/home
'''

import os
import sys
import urllib
import urllib2
import requests
import json
import tornado.concurrent
import tornado.httpclient
from tornado import gen

class AuthError(Exception):
    pass

class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(object):
    '''
        日常：https://login-test.alibaba-inc.com
        线上：https://login.alibaba-inc.com
    '''
    SSO_HOST = "https://login-test.alibaba-inc.com"

    LOGIN_URL              = SSO_HOST + '/ssoLogin.htm'
    COMMUNICATE_URL        = SSO_HOST + '/rpc/sso/communicate.json'
    VALIDATE_SSO_TOKEN_URL = SSO_HOST + '/rpc/ssoToken/validateSsoToken.json'

    '''
        换成自己的APP_CODE和APP_NAME
    '''
    APP_CODE = '5f4c3abddee641078ba36994c27ada0b'
    APP_NAME = 'oneday-workflow-allin'

    @gen.coroutine
    def authenticate_redirect(self, next):
        args = {
                'APP_NAME': self.APP_NAME,
                'BACK_URL': next,
                }
        self.redirect(self.LOGIN_URL + '?' + urllib.urlencode(args))

    @gen.coroutine
    def validate_sso_token(self, token):
        http = self.get_auth_http_client()
        args = {
                'SSO_TOKEN': token,
                'APP_CODE': self.APP_CODE,
                'RETURN_USER': 'true',
                }

        response = requests.post(self.VALIDATE_TOKEN_URL, args)
        result = response.json()
        if not result[u'hasError']:
            user = json.loads(result['content'])
            user = self.normalize_user(user)
            raise gen.Return(user)
        else:
            raise AuthError(result.get('content'))

    @gen.coroutine
    def get_authenticated_user(self, token):
        http = self.get_auth_http_client()
        args = {
                'SSO_TOKEN': token,
                'APP_CODE': self.APP_CODE,
                'RETURN_USER': 'true',
                }

        response = requests.post(self.COMMUNICATE_URL, args)
        result = response.json()
        if not result[u'hasError']:
            user = json.loads(result['content'])
            user = self.normalize_user(user)
            raise gen.Return(user)
        else:
            raise AuthError(result.get('content'))

    def normalize_user(self, user):
        result = {}
        for key in ('nickNameCn', 'tbWW'):
            if result.get('name'):
                break
            result['name'] = user.get(key)

        for key in ('emailAddr', 'account'):
            if result.get('email'):
                break
            result['email'] = user.get(key)

        result['realname'] = user.get('lastName')
        result['token'] = user.get('token')
        result['id'] = user.get('empId')
        result['department'] = user.get('depDesc')
        result["mobile"] = user.get("cellphone")
        return result

    def get_auth_http_client(self):
        return tornado.httpclient.AsyncHTTPClient()

def main():
    ''' main function
    '''
    print 'Done'

if __name__ == '__main__':
    main()
