# 语雀知识库导入功能设计方案

## 1. 概述

本方案设计语雀知识库的导入功能，支持单文档和目录两种导入方式，参考Notion导入的实现方式，实现完整的文档导入和同步更新流程。

## 2. 系统架构

```mermaid
sequenceDiagram
    participant Client
    participant WebUI
    participant API
    participant YuqueAPI
    participant Storage
    
    Client->>WebUI: 选择导入语雀文档
    WebUI->>API: 请求OAuth授权
    API->>YuqueAPI: 获取访问令牌
    YuqueAPI-->>API: 返回token
    API->>Storage: 保存认证信息
    
    Client->>WebUI: 选择导入范围
    WebUI->>API: 获取可导入文档列表
    API->>YuqueAPI: 查询知识库/文档
    YuqueAPI-->>API: 返回文档列表
    API-->>WebUI: 显示可选文档
    
    Client->>WebUI: 确认导入
    WebUI->>API: 提交导入请求
    API->>YuqueAPI: 获取文档内容
    YuqueAPI-->>API: 返回文档内容
    API->>Storage: 保存文档
    API->>Storage: 触发异步处理
```

## 3. 数据模型扩展

### 3.1 数据源类型

```python
class Document(db.Model):
    DATA_SOURCES = [
        "upload_file",
        "notion_import",
        "website_crawl",
        "yuque_import"  # 新增语雀导入类型
    ]
```

### 3.2 认证信息存储

```python
class DataSourceOauthBinding(db.Model):
    __tablename__ = "data_source_oauth_bindings"
    
    id = Column(UUID, primary_key=True)
    tenant_id = Column(UUID, nullable=False)
    provider = Column(String)  # 'yuque'
    access_token = Column(String)
    source_info = Column(JSON)  # 包含 user_id, space_id 等信息
```

### 3.3 文档源信息

```typescript
interface YuqueSourceInfo {
  type: 'yuque_import';
  info_list: {
    yuque_info_list: {
      space_id: string;      // 知识库ID
      docs: {
        doc_id: string;      // 文档ID
        doc_name: string;    // 文档名称
        doc_type: string;    // 'doc' | 'folder'
        parent_id?: string;  // 父级目录ID
      }[];
    }[];
  };
}
```

## 4. 接口设计

### 4.1 OAuth认证接口

```python
@api.resource('/yuque/auth/callback')
class YuqueAuthCallback(Resource):
    def get(self):
        """处理语雀OAuth回调"""
        code = request.args.get('code')
        
        # 获取访问令牌
        token = YuqueAPI.get_access_token(code)
        
        # 保存认证信息
        binding = DataSourceOauthBinding(
            tenant_id=current_user.current_tenant_id,
            provider='yuque',
            access_token=token
        )
        db.session.add(binding)
        db.session.commit()
```

### 4.2 文档列表接口

```python
@api.resource('/yuque/spaces/<space_id>/docs')
class YuqueDocumentList(Resource):
    def get(self, space_id):
        """获取知识库下的文档列表"""
        binding = DataSourceOauthBinding.get_by_tenant(
            current_user.current_tenant_id,
            'yuque'
        )
        
        # 获取文档列表
        yuque = YuqueAPI(binding.access_token)
        docs = yuque.get_docs(space_id)
        
        # 构建文档树
        return build_doc_tree(docs)
```

### 4.3 导入接口

```python
@api.resource('/datasets/<dataset_id>/yuque-import')
class YuqueImport(Resource):
    def post(self, dataset_id):
        """导入语雀文档"""
        args = request.get_json()
        
        # 验证数据集
        dataset = Dataset.query.get_or_404(dataset_id)
        
        # 处理导入
        documents = DocumentService.save_yuque_documents(
            dataset=dataset,
            yuque_info=args['yuque_info'],
            account=current_user
        )
        
        return {'status': 'success', 'count': len(documents)}
```

## 5. 核心实现

### 5.1 语雀API封装

```python
class YuqueAPI:
    def __init__(self, access_token: str):
        self.session = requests.Session()
        self.session.headers.update({
            'X-Auth-Token': access_token,
            'Accept': 'application/json'
        })
        self.base_url = 'https://yuque.alibaba-inc.com/api/v2'
    
    def get_docs(self, space_id: str) -> List[dict]:
        """获取知识库下的文档列表"""
        response = self.session.get(f'{self.base_url}/repos/{space_id}/docs')
        response.raise_for_status()
        return response.json()['data']
    
    def get_doc_detail(self, space_id: str, doc_id: str) -> dict:
        """获取文档详情"""
        response = self.session.get(
            f'{self.base_url}/repos/{space_id}/docs/{doc_id}'
        )
        response.raise_for_status()
        return response.json()['data']
```

### 5.2 文档导入处理

```python
class YuqueDocumentHandler:
    @staticmethod
    def handle_import(yuque_info: dict, dataset: Dataset) -> List[Document]:
        documents = []
        
        for space_info in yuque_info['yuque_info_list']:
            space_id = space_info['space_id']
            
            # 验证权限
            binding = DataSourceOauthBinding.query.filter(
                DataSourceOauthBinding.tenant_id == dataset.tenant_id,
                DataSourceOauthBinding.provider == 'yuque'
            ).first()
            
            yuque = YuqueAPI(binding.access_token)
            
            # 处理文档
            for doc in space_info['docs']:
                # 获取文档内容
                detail = yuque.get_doc_detail(space_id, doc['doc_id'])
                
                # 创建文档
                document = Document(
                    dataset_id=dataset.id,
                    name=doc['doc_name'],
                    data_source_type='yuque_import',
                    data_source_info=json.dumps({
                        'space_id': space_id,
                        'doc_id': doc['doc_id'],
                        'parent_id': doc.get('parent_id'),
                        'doc_type': doc['doc_type']
                    })
                )
                
                # 处理文档内容
                if doc['doc_type'] == 'doc':
                    content = detail['body']
                    segments = split_content(content)
                    document.segments = segments
                
                documents.append(document)
        
        return documents
```

### 5.3 同步更新实现

```python
@shared_task
def sync_yuque_document(document_id: str):
    """同步更新语雀文档"""
    document = Document.query.get(document_id)
    if document.data_source_type != 'yuque_import':
        return
    
    # 获取文档信息
    data_source_info = document.data_source_info_dict
    space_id = data_source_info['space_id']
    doc_id = data_source_info['doc_id']
    
    # 获取更新
    binding = DataSourceOauthBinding.query.filter(
        DataSourceOauthBinding.tenant_id == document.tenant_id,
        DataSourceOauthBinding.provider == 'yuque'
    ).first()
    
    yuque = YuqueAPI(binding.access_token)
    detail = yuque.get_doc_detail(space_id, doc_id)
    
    # 检查是否需要更新
    if detail['updated_at'] > document.updated_at:
        content = detail['body']
        segments = split_content(content)
        document.segments = segments
        document.updated_at = detail['updated_at']
        db.session.commit()
```

## 6. 前端实现

### 6.1 导入流程组件

```typescript
// YuqueImport.tsx
import React, { useState } from 'react';
import { Tree, Button, message } from 'antd';
import { useYuqueSpaces, useYuqueDocs } from '@/hooks/yuque';

export const YuqueImport: React.FC = () => {
  const [selectedDocs, setSelectedDocs] = useState<string[]>([]);
  const { spaces } = useYuqueSpaces();
  const { docs, loading } = useYuqueDocs();
  
  const handleImport = async () => {
    try {
      await api.post('/datasets/${datasetId}/yuque-import', {
        yuque_info: {
          yuque_info_list: [{
            space_id: currentSpace,
            docs: selectedDocs.map(id => ({
              doc_id: id,
              doc_name: docs[id].name,
              doc_type: docs[id].type
            }))
          }]
        }
      });
      message.success('导入成功');
    } catch (error) {
      message.error('导入失败');
    }
  };
  
  return (
    <div>
      <Tree
        checkable
        loading={loading}
        onCheck={setSelectedDocs}
        treeData={buildTreeData(docs)}
      />
      <Button 
        type="primary"
        onClick={handleImport}
        disabled={selectedDocs.length === 0}
      >
        导入选中文档
      </Button>
    </div>
  );
};
```

### 6.2 文档树构建

```typescript
// utils/yuque.ts
interface DocNode {
  id: string;
  name: string;
  type: 'doc' | 'folder';
  parent_id?: string;
  children?: DocNode[];
}

export function buildTreeData(docs: DocNode[]) {
  const map = new Map<string, DocNode>();
  const tree: DocNode[] = [];
  
  // 创建节点映射
  docs.forEach(doc => {
    map.set(doc.id, { ...doc, children: [] });
  });
  
  // 构建树结构
  docs.forEach(doc => {
    const node = map.get(doc.id)!;
    if (doc.parent_id) {
      const parent = map.get(doc.parent_id);
      if (parent) {
        parent.children?.push(node);
      }
    } else {
      tree.push(node);
    }
  });
  
  return tree;
}
```

## 7. 部署配置

### 7.1 环境变量

```bash
# 语雀API配置
YUQUE_APP_ID=your_app_id
YUQUE_APP_SECRET=your_app_secret
YUQUE_CALLBACK_URL=http://your-domain/api/yuque/auth/callback
```

### 7.2 数据库迁移

```python
"""add_yuque_import_type
Revision ID: xxx
"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # 添加语雀导入类型
    op.execute(
        "ALTER TYPE document_source_type ADD VALUE 'yuque_import'"
    )

def downgrade():
    # 不支持删除enum值
    pass
```

## 8. 使用说明

1. 配置语雀应用
   - 在语雀开放平台创建应用
   - 配置回调地址
   - 获取AppID和AppSecret

2. 授权流程
   - 点击"导入语雀文档"
   - 跳转到语雀授权页面
   - 同意授权后返回系统

3. 导入文档
   - 选择知识库
   - 勾选需要导入的文档/目录
   - 点击导入按钮

4. 注意事项
   - 目录导入会递归导入所有子文档
   - 文档更新会自动同步
   - 建议选择合适的索引模式

## 9. 后续优化

1. 性能优化
   - 批量导入优化
   - 增量同步优化
   - 缓存优化

2. 功能增强
   - 导入进度显示
   - 错误重试机制
   - 导入历史记录

3. 体验优化
   - 文档预览
   - 导入配置记忆
   - 状态即时更新
