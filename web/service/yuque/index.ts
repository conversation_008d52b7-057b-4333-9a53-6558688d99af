import { post } from '../base'
import type { YuqueKnowledgeDoc } from '@/models/datasets'

export const getBookTableOfContents = (bookId: string, token: string) => {
  return post<{
    content: {
      data: YuqueKnowledgeDoc[]
    }
  }>('/datasets/yuque/book/table-of-contents', {
    body: {
      book_id: bookId,
      token,
    },
  })
}

export const getDocumentContent = (url: string, token: string) => {
  try {
    return post<{
      content: any
    }>('/datasets/yuque/document/content', {
      body: {
        url,
        token,
      },
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}
