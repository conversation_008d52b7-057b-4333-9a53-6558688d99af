const translation = {
  input: 'WEJŚCIE',
  result: 'WYNI<PERSON>',
  detail: 'SZ<PERSON>ZEGÓŁY',
  tracing: 'ŚLEDZENIE',
  resultPanel: {
    status: 'STATUS',
    time: 'CZAS WYKONANIA',
    tokens: 'CAŁKOWITA LICZBA TOKENÓW',
  },
  meta: {
    title: 'METADANE',
    status: 'Status',
    version: '<PERSON><PERSON>ja',
    executor: 'W<PERSON>onaw<PERSON>',
    startTime: '<PERSON><PERSON> rozpoczę<PERSON>',
    time: '<PERSON><PERSON> trwania',
    tokens: 'Licz<PERSON> tokenów',
    steps: 'Kroki wykonania',
  },
  resultEmpty: {
    title: 'To wykonanie generuje tylko format JSON,',
    tipLeft: 'proszę przejdź do ',
    link: 'panelu szczegółów',
    tipRight: ' aby je zobaczyć.',
  },
  circularInvocationTip: 'W bieżącym przepływie pracy istnieje cykliczne wywoływanie narzędzi/węzł<PERSON>.',
  actionLogs: '<PERSON>zienniki akcji',
}

export default translation
