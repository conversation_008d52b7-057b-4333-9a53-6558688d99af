'use client'
import { useCallback, useState } from 'react'
import { useContext } from 'use-context-selector'
import { XMarkIcon } from '@heroicons/react/24/outline'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { ToastContext } from '@/app/components/base/toast'
import { updateWorkspaceName } from '@/service/yy-workspace'

type IRenameModalProps = {
  workspace: {
    id: string
    name: string
  }
  onCancel: () => void
  onSuccess: () => void
}

const RenameModal = ({
  workspace,
  onCancel,
  onSuccess,
}: IRenameModalProps) => {
  const { notify } = useContext(ToastContext)
  const [workspaceName, setWorkspaceName] = useState<string>(workspace.name)
  const [isLoading, setIsLoading] = useState(false)

  const handleRename = useCallback(async () => {
    if (workspaceName) {
      if (workspaceName === workspace.name) {
        onCancel()
        return
      }

      try {
        setIsLoading(true)
        const result = await updateWorkspaceName({
          new_name: workspaceName,
        }) as { message: string; tenant: any }
        setIsLoading(false)

        if (result && result.message === 'Workspace name updated successfully') {
          notify({ type: 'success', message: '工作空间名称已更新' })
          onSuccess()
        }
      }
      catch (e) {
        setIsLoading(false)
        notify({ type: 'error', message: '更新工作空间名称失败' })
      }
    }
    else {
      notify({ type: 'error', message: '请输入工作空间名称' })
    }
  }, [notify, onCancel, onSuccess, workspace.id, workspace.name, workspaceName])

  return (
    <div>
      <Modal overflowVisible isShow onClose={() => {}} className='!max-w-[520px]'>
        <div className='flex justify-between mb-2'>
          <div className='text-xl font-semibold text-gray-900'>修改工作空间名称</div>
          <XMarkIcon className='w-4 h-4 cursor-pointer' onClick={onCancel} />
        </div>
        <div>
          <div className='mb-2 text-sm font-medium text-gray-900'>工作空间名称</div>
          <div className='mb-8 flex items-stretch'>
            <input
              value={workspaceName}
              onChange={(e) => {
                setWorkspaceName(e.target.value)
              }}
              className='w-full h-10 px-3 text-sm font-normal bg-gray-100 rounded-lg grow focus:outline-none focus:border-blue-500'
              placeholder="请输入工作空间名称"
            />
          </div>
          <Button
            tabIndex={0}
            className='w-full h-10'
            onClick={handleRename}
            disabled={!workspaceName || isLoading}
            variant='primary'
            loading={isLoading}
          >
            确定
          </Button>
        </div>
      </Modal>
    </div>
  )
}

export default RenameModal
