.modalWrapper {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modalMask {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal {
  @apply relative bg-white rounded-lg shadow-xl w-[640px];
  height: 50vh;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-100 shrink-0;
}

.title {
  @apply text-base font-medium text-gray-900;
}

.closeIcon {
  @apply flex items-center justify-center w-6 h-6 text-gray-500 cursor-pointer hover:text-gray-700;
}

.searchWrapper {
  @apply flex items-center px-6 py-3 border-b border-gray-100 shrink-0;
}

.searchInput {
  @apply ml-2 flex-1 text-sm text-gray-900 bg-transparent border-none outline-none placeholder-gray-400;
}

.modalBody {
  @apply p-6 overflow-y-auto;
  flex: 1;
}

.docList {
  @apply space-y-2;
}

.docItem {
  @apply flex items-center p-3 rounded-lg border border-gray-100 hover:border-gray-200 hover:bg-gray-50;
}

.titleItem {
  @apply font-medium bg-gray-50;
}

.checkbox {
  @apply mr-2 h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600;
}

.docTitle {
  @apply text-sm text-gray-900;
}

.level1 {
  padding-left: 1.5rem;
}

.level2 {
  padding-left: 3rem;
}

.level3 {
  padding-left: 4.5rem;
}

.modalFooter {
  @apply flex items-center justify-end px-6 py-4 border-t border-gray-100 shrink-0;
}

.expandIcon {
  @apply flex items-center justify-center w-5 h-5 mr-2 text-gray-500 cursor-pointer hover:text-gray-700 transition-transform duration-200;
}

.expandIcon.expanded {
  @apply transform rotate-90;
}
