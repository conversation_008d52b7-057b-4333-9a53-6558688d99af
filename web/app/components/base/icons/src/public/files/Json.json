{"icon": {"type": "element", "isRootNode": true, "name": "svg", "attributes": {"width": "32", "height": "34", "viewBox": "0 0 32 34", "fill": "none", "xmlns": "http://www.w3.org/2000/svg"}, "children": [{"type": "element", "name": "g", "attributes": {"filter": "url(#filter0_d_3055_14428)"}, "children": [{"type": "element", "name": "path", "attributes": {"d": "M4 7.73349C4 5.49329 4 4.37318 4.43597 3.51753C4.81947 2.76489 5.43139 2.15296 6.18404 1.76947C7.03969 1.3335 8.15979 1.3335 10.4 1.3335H18.6667L28 10.6668V24.2668C28 26.507 28 27.6271 27.564 28.4828C27.1805 29.2354 26.5686 29.8474 25.816 30.2309C24.9603 30.6668 23.8402 30.6668 21.6 30.6668H10.4C8.15979 30.6668 7.03969 30.6668 6.18404 30.2309C5.43139 29.8474 4.81947 29.2354 4.43597 28.4828C4 27.6271 4 26.507 4 24.2668V7.73349Z", "fill": "#2D2D2E"}, "children": []}]}, {"type": "element", "name": "g", "attributes": {"opacity": "0.96"}, "children": [{"type": "element", "name": "path", "attributes": {"d": "M9.83907 22.0479V18.3039H8.43907V22.0159C8.43907 22.5599 8.12707 22.7999 7.69507 22.7999C7.38307 22.7999 7.23907 22.6879 7.06307 22.5119L6.14307 23.4239C6.60707 23.8879 7.03107 24.0479 7.69507 24.0479C8.76707 24.0479 9.83907 23.3999 9.83907 22.0479Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M14.7321 22.2559C14.7321 21.7279 14.6121 21.3039 14.3081 21.0079C14.0681 20.7679 13.7001 20.6079 13.1881 20.5359L12.5001 20.4399C12.3001 20.4159 12.1801 20.3439 12.1081 20.2719C12.0201 20.1839 11.9961 20.0799 11.9961 20.0079C11.9961 19.7599 12.1961 19.4799 12.6841 19.4799C12.9321 19.4799 13.4041 19.4559 13.7641 19.8159L14.6441 18.9359C14.1561 18.4479 13.5401 18.2559 12.7241 18.2559C11.4281 18.2559 10.6441 19.0159 10.6441 20.0559C10.6441 20.5439 10.7721 20.9279 11.0361 21.1999C11.2921 21.4639 11.6761 21.6319 12.1801 21.7039L12.8681 21.7999C13.0521 21.8239 13.1721 21.8799 13.2441 21.9519C13.3241 22.0399 13.3561 22.1519 13.3561 22.2879C13.3561 22.6159 13.0921 22.7999 12.5401 22.7999C12.0841 22.7999 11.5641 22.6959 11.2681 22.3999L10.3721 23.2959C10.9481 23.8879 11.6601 24.0479 12.5321 24.0479C13.7321 24.0479 14.7321 23.4159 14.7321 22.2559Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M19.8023 21.1519C19.8023 20.2959 19.8263 19.4959 19.2263 18.8959C18.8103 18.4799 18.3303 18.2559 17.6343 18.2559C16.9383 18.2559 16.4583 18.4799 16.0423 18.8959C15.4423 19.4959 15.4663 20.2959 15.4663 21.1519C15.4663 22.0079 15.4423 22.8079 16.0423 23.4079C16.4583 23.8239 16.9383 24.0479 17.6343 24.0479C18.3303 24.0479 18.8103 23.8239 19.2263 23.4079C19.8263 22.8079 19.8023 22.0079 19.8023 21.1519ZM18.4023 21.1519C18.4023 22.1919 18.3223 22.3759 18.1943 22.5439C18.0903 22.6799 17.8903 22.7999 17.6343 22.7999C17.3783 22.7999 17.1783 22.6799 17.0743 22.5439C16.9463 22.3759 16.8663 22.1919 16.8663 21.1519C16.8663 20.1119 16.9463 19.9199 17.0743 19.7519C17.1783 19.6159 17.3783 19.5039 17.6343 19.5039C17.8903 19.5039 18.0903 19.6159 18.1943 19.7519C18.3223 19.9199 18.4023 20.1119 18.4023 21.1519Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M25.2154 23.9999V18.3039H23.8154V21.1679L21.9914 18.3039H20.7674V23.9999H22.1674V21.1359L23.9914 23.9999H25.2154Z", "fill": "white"}, "children": []}]}, {"type": "element", "name": "path", "attributes": {"opacity": "0.5", "d": "M18.6665 1.3335L27.9998 10.6668H21.3332C19.8604 10.6668 18.6665 9.47292 18.6665 8.00016V1.3335Z", "fill": "white"}, "children": []}, {"type": "element", "name": "defs", "attributes": {}, "children": [{"type": "element", "name": "filter", "attributes": {"id": "filter0_d_3055_14428", "x": "2", "y": "0.333496", "width": "28", "height": "33.3335", "filterUnits": "userSpaceOnUse", "color-interpolation-filters": "sRGB"}, "children": [{"type": "element", "name": "feFlood", "attributes": {"flood-opacity": "0", "result": "BackgroundImageFix"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dy": "1"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "1"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "BackgroundImageFix", "result": "effect1_dropShadow_3055_14428"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in": "SourceGraphic", "in2": "effect1_dropShadow_3055_14428", "result": "shape"}, "children": []}]}]}]}, "name": "Json"}