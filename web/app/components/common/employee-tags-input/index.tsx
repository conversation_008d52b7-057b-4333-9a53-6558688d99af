'use client'
import { useCallback, useEffect, useRef, useState } from 'react'
import { debounce } from 'lodash'
import cn from '@/utils/classnames'
import { queryEmployee } from '@/service/common'

export type Employee = {
  name: string
  nickName: string
  workNo: string
  accountMail: string
  buMail?: string
  avatar?: string
}

export type EmployeeTag = {
  id: string
  name: string
  nickName: string
  label: string
  value: string
  email: string
  workNo?: string
  avatar?: string
}

export type EmployeeTagsInputProps = {
  selectedTags: EmployeeTag[]
  onSelect: (tag: EmployeeTag) => void
  onRemove: (index: number) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  searchingText?: string
  noResultsText?: string
}

const EmployeeTagsInput = ({
  selectedTags = [],
  onSelect,
  onRemove,
  placeholder = '搜索员工...',
  className = '',
  disabled = false,
  searchingText = '搜索中...',
  noResultsText = '未找到匹配结果',
}: EmployeeTagsInputProps) => {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<Employee[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [activeIndex, setActiveIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  // 搜索API调用
  const handleSearch = useCallback(async (searchValue: string) => {
    setIsSearching(true)
    setIsOpen(true)
    try {
      const { result, fuzzy_query_result } = await queryEmployee({
        url: '/workspaces/current/members/fuzzy-query-employee',
        params: { value: searchValue },
      })
      if (result === 'success' && fuzzy_query_result)
        setSuggestions(fuzzy_query_result)
      else
        setSuggestions([])
    }
    catch (error) {
      console.error('搜索失败:', error)
      setSuggestions([])
    }
    finally {
      setIsSearching(false)
    }
  }, [])
  // 防抖搜索函数
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      if (!value || value.length < 2) {
        setSuggestions([])
        setIsOpen(false)
        return
      }
      handleSearch(value)
    }, 300),
    [handleSearch],
  )
  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    debouncedSearch(value)
  }, [debouncedSearch])
  // 选择员工
  const handleSelectEmployee = useCallback((employee: Employee) => {
    // 转换为标签格式
    const newTag: EmployeeTag = {
      id: employee.workNo,
      name: employee.name,
      nickName: employee.nickName,
      label: employee.nickName,
      value: employee.accountMail,
      email: employee.accountMail,
      workNo: employee.workNo,
      avatar: employee.avatar || `https://work.alibaba-inc.com/photo/${employee.workNo}.200x200.jpg`,
    }
    onSelect(newTag)
    // 重置状态
    setQuery('')
    setSuggestions([])
    setIsOpen(false)
    setActiveIndex(-1)
    // 重新聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus()
    }, 0)
  }, [onSelect])
  // 处理键盘导航
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isOpen)
      return
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setActiveIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev,
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setActiveIndex(prev => (prev > 0 ? prev - 1 : 0))
        break
      case 'Enter':
        e.preventDefault()
        if (activeIndex >= 0 && activeIndex < suggestions.length)
          handleSelectEmployee(suggestions[activeIndex])
        break
      case 'Escape':
        e.preventDefault()
        setIsOpen(false)
        break
      default:
        break
    }
  }, [isOpen, suggestions, activeIndex, handleSelectEmployee])
  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current
        && !suggestionsRef.current.contains(event.target as Node)
        && inputRef.current
        && !inputRef.current.contains(event.target as Node)
      )
        setIsOpen(false)
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className={cn('relative w-full', className)}>
      {/* 标签显示区域 */}
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedTags.map((tag, index) => (
          <div
            key={tag.id || index}
            className="flex items-center bg-gray-100 rounded-md px-2 py-1"
          >
            <span>{tag.label}</span>
            <button
              type="button"
              onClick={() => onRemove(index)}
              className="ml-2 text-gray-500 hover:text-gray-700"
              disabled={disabled}
            >
              ×
            </button>
          </div>
        ))}
      </div>

      {/* 搜索输入框 - 确保撑满宽度 */}
      <div className="relative w-full">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length >= 2 && setIsOpen(true)}
          placeholder={placeholder}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
          disabled={disabled}
        />

        {/* 搜索结果下拉框 */}
        {isOpen && (
          <div
            ref={suggestionsRef}
            className="absolute mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto z-50"
          >
            {isSearching
              ? (
                <div className="py-2 px-4 text-gray-500">{searchingText}</div>
              )
              : suggestions.length === 0
                ? (
                  <div className="py-2 px-4 text-gray-500">{noResultsText}</div>
                )
                : (
                  suggestions.map((employee, index) => (
                    <div
                      key={employee.workNo}
                      className={cn(
                        'flex items-center px-4 py-2 hover:bg-gray-100 cursor-pointer',
                        index === activeIndex ? 'bg-gray-100' : '',
                      )}
                      onClick={() => handleSelectEmployee(employee)}
                    >
                      <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
                        {employee.workNo
                          ? (
                        // eslint-disable-next-line @next/next/no-img-element
                            <img
                              src={`https://work.alibaba-inc.com/photo/${employee.workNo}.200x200.jpg`}
                              alt=''
                              className='w-full h-full object-cover'
                            />
                          )
                          : (
                            <div className='w-full h-full bg-gray-200 flex items-center justify-center text-gray-500'>
                              {employee.nickName?.[0] || '?'}
                            </div>
                          )}
                      </div>
                      <div>
                        <div className='text-sm font-medium text-gray-900'>
                          {employee.name} ({employee.nickName})
                        </div>
                        {employee.workNo && (
                          <div className='text-xs text-gray-500'>{employee.workNo}</div>
                        )}
                      </div>
                    </div>
                  ))
                )}
          </div>
        )}
      </div>
    </div>
  )
}

export default EmployeeTagsInput
