import { useMemo } from 'react'
import { useGetLanguage } from '@/context/i18n'
import { BlockEnum } from '@/app/components/workflow/types'

export const useNodeHelpLink = (nodeType: BlockEnum) => {
  const language = useGetLanguage()
  const prefixLink = useMemo(() => {
    if (language === 'zh_<PERSON>')
      return 'https://alidocs.dingtalk.com/i/nodes/'

    return 'https://alidocs.dingtalk.com/i/nodes/'
  }, [language])
  const linkMap = useMemo(() => {
    if (language === 'zh_Hans') {
      return {
        [BlockEnum.Start]: 'N7dx2rn0JbxOaqnACLn4maGgWMGjLRb3',
        [BlockEnum.End]: 'YQBnd5ExVEjea40qCMvlZnm2JyeZqMmz',
        [BlockEnum.Answer]: '93NwLYZXWyxXroNzCX6zlPr98kyEqBQm',
        [BlockEnum.LLM]: 'XPwkYGxZV347LdvpH4eXA5lbJAgozOKL',
        [BlockEnum.KnowledgeRetrieval]: 'QOG9lyrgJPPNL2rXInd6MMDDJzN67Mw4',
        [BlockEnum.QuestionClassifier]: '14lgGw3P8vxjwogPCjXe0qZeV5daZ90D',
        [BlockEnum.IfElse]: 'EpGBa2Lm8aZxe5myCoaKloq7WgN7R35y',
        [BlockEnum.Code]: 'a9E05BDRVQRkezKGC7gQN1rPJ63zgkYA',
        [BlockEnum.TemplateTransform]: 'Y1OQX0akWmzdBowLFELB3ZLYVGlDd3mE',
        [BlockEnum.VariableAssigner]: '20eMKjyp810mMdK4HkXqAwk2JxAZB1Gv',
        [BlockEnum.VariableAggregator]: 'QG53mjyd800agdlKHj9Ykv6986zbX04v',
        [BlockEnum.Assigner]: '20eMKjyp810mMdK4HkXqAwk2JxAZB1Gv',
        [BlockEnum.Iteration]: '2Amq4vjg89jyZdNnCrN5wax9W3kdP0wQ',
        [BlockEnum.Loop]: 'dpYLaezmVNRMGX56CaReDdj0VrMqPxX6',
        [BlockEnum.ParameterExtractor]: 'YndMj49yWjlAYoxjtz6k3DXNJ3pmz5aA',
        [BlockEnum.HttpRequest]: 'Qnp9zOoBVBDEydnQUr2vpXAk81DK0g6l',
        [BlockEnum.Tool]: 'Y1OQX0akWmzdBowLFELBjLQXVGlDd3mE',
        [BlockEnum.DocExtractor]: 'N7dx2rn0JbxOaqnACLn4wb7YWMGjLRb3',
        [BlockEnum.ListFilter]: 'kDnRL6jAJMLgNkw7tZv7ywK0VyMoPYe1',
        [BlockEnum.Agent]: 'jb9Y4gmKWrx9eo4dC9XR3OMRJGXn6lpz',
      }
    }

    return {
      [BlockEnum.Start]: 'N7dx2rn0JbxOaqnACLn4maGgWMGjLRb3',
      [BlockEnum.End]: 'YQBnd5ExVEjea40qCMvlZnm2JyeZqMmz',
      [BlockEnum.Answer]: '93NwLYZXWyxXroNzCX6zlPr98kyEqBQm',
      [BlockEnum.LLM]: 'XPwkYGxZV347LdvpH4eXA5lbJAgozOKL',
      [BlockEnum.KnowledgeRetrieval]: 'QOG9lyrgJPPNL2rXInd6MMDDJzN67Mw4',
      [BlockEnum.QuestionClassifier]: '14lgGw3P8vxjwogPCjXe0qZeV5daZ90D',
      [BlockEnum.IfElse]: 'EpGBa2Lm8aZxe5myCoaKloq7WgN7R35y',
      [BlockEnum.Code]: 'a9E05BDRVQRkezKGC7gQN1rPJ63zgkYA',
      [BlockEnum.TemplateTransform]: 'Y1OQX0akWmzdBowLFELB3ZLYVGlDd3mE',
      [BlockEnum.VariableAssigner]: '20eMKjyp810mMdK4HkXqAwk2JxAZB1Gv',
      [BlockEnum.VariableAggregator]: 'QG53mjyd800agdlKHj9Ykv6986zbX04v',
      [BlockEnum.Assigner]: '20eMKjyp810mMdK4HkXqAwk2JxAZB1Gv',
      [BlockEnum.Iteration]: '2Amq4vjg89jyZdNnCrN5wax9W3kdP0wQ',
      [BlockEnum.Loop]: 'dpYLaezmVNRMGX56CaReDdj0VrMqPxX6',
      [BlockEnum.ParameterExtractor]: 'YndMj49yWjlAYoxjtz6k3DXNJ3pmz5aA',
      [BlockEnum.HttpRequest]: 'Qnp9zOoBVBDEydnQUr2vpXAk81DK0g6l',
      [BlockEnum.Tool]: 'Y1OQX0akWmzdBowLFELBjLQXVGlDd3mE',
      [BlockEnum.DocExtractor]: 'N7dx2rn0JbxOaqnACLn4wb7YWMGjLRb3',
      [BlockEnum.ListFilter]: 'kDnRL6jAJMLgNkw7tZv7ywK0VyMoPYe1',
      [BlockEnum.Agent]: 'jb9Y4gmKWrx9eo4dC9XR3OMRJGXn6lpz',
    }
  }, [language]) as Record<string, string>

  const link = linkMap[nodeType]

  if (!link)
    return ''

  return `${prefixLink}${link}`
}
