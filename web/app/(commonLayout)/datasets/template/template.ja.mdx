{/**
  * @typedef Props
  * @property {string} apiBaseUrl
  */}

import { CodeGroup } from '@/app/components/develop/code.tsx'
import { Row, Col, Properties, Property, Heading, SubProperty, PropertyInstruction, Paragraph } from '@/app/components/develop/md.tsx'

# ナレッジ API

<div>
  ### 認証

  Dify のサービス API は `API-Key` を使用して認証します。

  開発者は、`API-Key` をクライアント側で共有または保存するのではなく、バックエンドに保存することを推奨します。これにより、`API-Key` の漏洩による財産損失を防ぐことができます。

  すべての API リクエストには、以下のように **`Authorization`** HTTP ヘッダーに `API-Key` を含める必要があります：

  <CodeGroup title="コード">
    ```javascript
      Authorization: Bearer {API_KEY}

    ```
  </CodeGroup>
</div>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/document/create-by-text'
  method='POST'
  title='テキストからドキュメントを作成'
  name='#create-by-text'
/>
<Row>
  <Col>
    この API は既存のナレッジに基づいており、このナレッジを基にテキストを使用して新しいドキュメントを作成します。

    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='name' type='string' key='name'>
        ドキュメント名
      </Property>
      <Property name='text' type='string' key='text'>
        ドキュメント内容
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        インデックスモード
          - <code>high_quality</code> 高品質: 埋め込みモデルを使用してベクトルデータベースインデックスを構築
          - <code>economy</code> 経済: キーワードテーブルインデックスの反転インデックスを構築
      </Property>
      <Property name='doc_form' type='string' key='doc_form'>
        インデックス化された内容の形式
          - <code>text_model</code> テキストドキュメントは直接埋め込まれます; `economy` モードではこの形式がデフォルト
          - <code>hierarchical_model</code> 親子モード
          - <code>qa_model</code> Q&A モード: 分割されたドキュメントの質問と回答ペアを生成し、質問を埋め込みます
      </Property>
      <Property name='doc_language' type='string' key='doc_language'>
        Q&A モードでは、ドキュメントの言語を指定します。例: <code>English</code>, <code>Chinese</code>
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        処理ルール
          - <code>mode</code> (string) クリーニング、セグメンテーションモード、自動 / カスタム
          - <code>rules</code> (object) カスタムルール (自動モードでは、このフィールドは空)
            - <code>pre_processing_rules</code> (array[object]) 前処理ルール
              - <code>id</code> (string) 前処理ルールの一意識別子
                - 列挙
                  - <code>remove_extra_spaces</code> 連続するスペース、改行、タブを置換
                  - <code>remove_urls_emails</code> URL、メールアドレスを削除
              - <code>enabled</code> (bool) このルールを選択するかどうか。ドキュメント ID が渡されない場合、デフォルト値を表します。
            - <code>segmentation</code> (object) セグメンテーションルール
              - <code>separator</code> カスタムセグメント識別子。現在は 1 つの区切り文字のみ設定可能。デフォルトは \n
              - <code>max_tokens</code> 最大長 (トークン) デフォルトは 1000
            - <code>parent_mode</code> 親チャンクの検索モード: <code>full-doc</code> 全文検索 / <code>paragraph</code> 段落検索
            - <code>subchunk_segmentation</code> (object) 子チャンクルール
              - <code>separator</code> セグメンテーション識別子。現在は 1 つの区切り文字のみ許可。デフォルトは <code>***</code>
              - <code>max_tokens</code> 最大長 (トークン) は親チャンクの長さより短いことを検証する必要があります
              - <code>chunk_overlap</code> 隣接するチャンク間の重複を定義 (オプション)
      </Property>
      <PropertyInstruction>ナレッジベースにパラメータが設定されていない場合、最初のアップロードには以下のパラメータを提供する必要があります。提供されない場合、デフォルトパラメータが使用されます。</PropertyInstruction>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        検索モデル
          - <code>search_method</code> (string) 検索方法
            - <code>hybrid_search</code> ハイブリッド検索
            - <code>semantic_search</code> セマンティック検索
            - <code>full_text_search</code> 全文検索
          - <code>reranking_enable</code> (bool) 再ランキングを有効にするかどうか
          - <code>reranking_mode</code> (object) 再ランキングモデル構成
            - <code>reranking_provider_name</code> (string) 再ランキングモデルプロバイダー
            - <code>reranking_model_name</code> (string) 再ランキングモデル名
          - <code>top_k</code> (int) 返される結果の数
          - <code>score_threshold_enabled</code> (bool) スコア閾値を有効にするかどうか
          - <code>score_threshold</code> (float) スコア閾値
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        埋め込みモデル名
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        埋め込みモデルプロバイダー
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/document/create-by-text"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "text","text": "text","indexing_technique": "high_quality","process_rule": {"mode": "automatic"}}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-text' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "text",
        "text": "text",
        "indexing_technique": "high_quality",
        "process_rule": {
            "mode": "automatic"
        }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
            "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "text.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695690280,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/document/create-by-file'
  method='POST'
  title='ファイルからドキュメントを作成'
  name='#create-by-file'
/>
<Row>
  <Col>
    この API は既存のナレッジに基づいており、このナレッジを基にファイルを使用して新しいドキュメントを作成します。

    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='data' type='multipart/form-data json string' key='data'>
        - <code>original_document_id</code> 元のドキュメント ID (オプション)
          - ドキュメントを再アップロードまたはクリーニングとセグメンテーション構成を変更するために使用されます。欠落している情報は元のドキュメントからコピーされます。
          - 元のドキュメントはアーカイブされたドキュメントであってはなりません。
          - original_document_id が渡された場合、更新操作が実行されます。process_rule は入力可能な項目です。入力されない場合、元のドキュメントのセグメンテーション方法がデフォルトで使用されます。
          - original_document_id が渡されない場合、新しい操作が実行され、process_rule が必要です。

        - <code>indexing_technique</code> インデックスモード
          - <code>high_quality</code> 高品質: 埋め込みモデルを使用してベクトルデータベースインデックスを構築
          - <code>economy</code> 経済: キーワードテーブルインデックスの反転インデックスを構築

        - <code>doc_form</code> インデックス化された内容の形式
          - <code>text_model</code> テキストドキュメントは直接埋め込まれます; `economy` モードではこの形式がデフォルト
          - <code>hierarchical_model</code> 親子モード
          - <code>qa_model</code> Q&A モード: 分割されたドキュメントの質問と回答ペアを生成し、質問を埋め込みます

        - <code>doc_language</code> Q&A モードでは、ドキュメントの言語を指定します。例: <code>English</code>, <code>Chinese</code>

        - <code>process_rule</code> 処理ルール
          - <code>mode</code> (string) クリーニング、セグメンテーションモード、自動 / カスタム
          - <code>rules</code> (object) カスタムルール (自動モードでは、このフィールドは空)
            - <code>pre_processing_rules</code> (array[object]) 前処理ルール
              - <code>id</code> (string) 前処理ルールの一意識別子
                - 列挙
                  - <code>remove_extra_spaces</code> 連続するスペース、改行、タブを置換
                  - <code>remove_urls_emails</code> URL、メールアドレスを削除
              - <code>enabled</code> (bool) このルールを選択するかどうか。ドキュメント ID が渡されない場合、デフォルト値を表します。
            - <code>segmentation</code> (object) セグメンテーションルール
              - <code>separator</code> カスタムセグメント識別子。現在は 1 つの区切り文字のみ設定可能。デフォルトは \n
              - <code>max_tokens</code> 最大長 (トークン) デフォルトは 1000
            - <code>parent_mode</code> 親チャンクの検索モード: <code>full-doc</code> 全文検索 / <code>paragraph</code> 段落検索
            - <code>subchunk_segmentation</code> (object) 子チャンクルール
              - <code>separator</code> セグメンテーション識別子。現在は 1 つの区切り文字のみ許可。デフォルトは <code>***</code>
              - <code>max_tokens</code> 最大長 (トークン) は親チャンクの長さより短いことを検証する必要があります
              - <code>chunk_overlap</code> 隣接するチャンク間の重複を定義 (オプション)
      </Property>
      <Property name='file' type='multipart/form-data' key='file'>
        アップロードする必要があるファイル。
      </Property>
      <PropertyInstruction>ナレッジベースにパラメータが設定されていない場合、最初のアップロードには以下のパラメータを提供する必要があります。提供されない場合、デフォルトパラメータが使用されます。</PropertyInstruction>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        検索モデル
          - <code>search_method</code> (string) 検索方法
            - <code>hybrid_search</code> ハイブリッド検索
            - <code>semantic_search</code> セマンティック検索
            - <code>full_text_search</code> 全文検索
          - <code>reranking_enable</code> (bool) 再ランキングを有効にするかどうか
          - <code>reranking_mode</code> (object) 再ランキングモデル構成
            - <code>reranking_provider_name</code> (string) 再ランキングモデルプロバイダー
            - <code>reranking_model_name</code> (string) 再ランキングモデル名
          - <code>top_k</code> (int) 返される結果の数
          - <code>score_threshold_enabled</code> (bool) スコア閾値を有効にするかどうか
          - <code>score_threshold</code> (float) スコア閾値
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        埋め込みモデル名
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        埋め込みモデルプロバイダー
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/document/create-by-file"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'data="{"indexing_technique":"high_quality","process_rule":{"rules":{"pre_processing_rules":[{"id":"remove_extra_spaces","enabled":true},{"id":"remove_urls_emails","enabled":true}],"segmentation":{"separator":"###","max_tokens":500}},"mode":"custom"}}";type=text/plain' \\\n--form 'file=@"/path/to/file"'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-file' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'data="{\"name\":\"Dify\",\"indexing_technique\":\"high_quality\",\"process_rule\":{\"rules\":{\"pre_processing_rules\":[{\"id\":\"remove_extra_spaces\",\"enabled\":true},{\"id\":\"remove_urls_emails\",\"enabled\":true}],\"segmentation\":{\"separator\":\"###\",\"max_tokens\":500}},\"mode\":\"custom\"}}";type=text/plain' \
    --form 'file=@"/path/to/file"'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "Dify.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets'
  method='POST'
  title='空のナレッジベースを作成'
  name='#create_empty_dataset'
/>
<Row>
  <Col>
    ### リクエストボディ
    <Properties>
      <Property name='name' type='string' key='name'>
        ナレッジ名
      </Property>
      <Property name='description' type='string' key='description'>
        ナレッジの説明 (オプション)
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        インデックス技術 (オプション)
          - <code>high_quality</code> 高品質
          - <code>economy</code> 経済
      </Property>
      <Property name='permission' type='string' key='permission'>
        権限
          - <code>only_me</code> 自分のみ
          - <code>all_team_members</code> すべてのチームメンバー
          - <code>partial_members</code> 一部のメンバー
      </Property>
      <Property name='provider' type='string' key='provider'>
        プロバイダー (オプション、デフォルト: vendor)
          - <code>vendor</code> ベンダー
          - <code>external</code> 外部ナレッジ
      </Property>
      <Property name='external_knowledge_api_id' type='str' key='external_knowledge_api_id'>
        外部ナレッジ API ID (オプション)
      </Property>
      <Property name='external_knowledge_id' type='str' key='external_knowledge_id'>
        外部ナレッジ ID (オプション)
      </Property>
      <Property name='embedding_model' type='str' key='embedding_model'>
        埋め込みモデル名（任意）
      </Property>
      <Property name='embedding_provider_name' type='str' key='embedding_provider_name'>
        埋め込みモデルのプロバイダ名（任意）
      </Property>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        検索モデル（任意）
          - <code>search_method</code> (文字列) 検索方法
            - <code>hybrid_search</code> ハイブリッド検索
            - <code>semantic_search</code> セマンティック検索
            - <code>full_text_search</code> 全文検索
          - <code>reranking_enable</code> (ブール値) リランキングを有効にするかどうか
          - <code>reranking_model</code> (オブジェクト) リランクモデルの設定
              - <code>reranking_provider_name</code> (文字列) リランクモデルのプロバイダ
              - <code>reranking_model_name</code> (文字列) リランクモデル名
          - <code>top_k</code> (整数) 返される結果の数
          - <code>score_threshold_enabled</code> (ブール値) スコア閾値を有効にするかどうか
          - <code>score_threshold</code> (浮動小数点数) スコア閾値
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "name", "permission": "only_me"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${apiBaseUrl}/v1/datasets' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "name": "name",
      "permission": "only_me"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "id": "",
      "name": "name",
      "description": null,
      "provider": "vendor",
      "permission": "only_me",
      "data_source_type": null,
      "indexing_technique": null,
      "app_count": 0,
      "document_count": 0,
      "word_count": 0,
      "created_by": "",
      "created_at": **********,
      "updated_by": "",
      "updated_at": **********,
      "embedding_model": null,
      "embedding_model_provider": null,
      "embedding_available": null
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets'
  method='GET'
  title='ナレッジベースリストを取得'
  name='#dataset_list'
/>
<Row>
  <Col>
    ### クエリ
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        検索キーワード、オプション
      </Property>
      <Property name='tag_ids' type='array[string]' key='tag_ids'>
        タグIDリスト、オプション
      </Property>
      <Property name='page' type='string' key='page'>
        ページ番号、オプション、デフォルト1
      </Property>
      <Property name='limit' type='string' key='limit'>
        返されるアイテム数、オプション、デフォルト20、範囲1-100
      </Property>
      <Property name='include_all' type='boolean' key='include_all'>
        すべてのデータセットを含めるかどうか（所有者のみ有効）、オプション、デフォルトはfalse
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets?page=1&limit=20' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "",
          "name": "name",
          "description": "desc",
          "permission": "only_me",
          "data_source_type": "upload_file",
          "indexing_technique": "",
          "app_count": 2,
          "document_count": 10,
          "word_count": 1200,
          "created_by": "",
          "created_at": "",
          "updated_by": "",
          "updated_at": ""
        },
        ...
      ],
      "has_more": true,
      "limit": 20,
      "total": 50,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}'
  method='DELETE'
  title='ナレッジベースを削除'
  name='#delete_dataset'
/>
<Row>
  <Col>
    ### パラメータ
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="DELETE"
      label="/datasets/{dataset_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```text {{ title: 'Response' }}
    204 No Content
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/update-by-text'
  method='POST'
  title='テキストでドキュメントを更新'
  name='#update-by-text'
/>
<Row>
  <Col>
    この API は既存のナレッジに基づいており、このナレッジを基にテキストを使用してドキュメントを更新します。

    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='name' type='string' key='name'>
        ドキュメント名 (オプション)
      </Property>
      <Property name='text' type='string' key='text'>
        ドキュメント内容 (オプション)
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        処理ルール
          - <code>mode</code> (string) クリーニング、セグメンテーションモード、自動 / カスタム
          - <code>rules</code> (object) カスタムルール (自動モードでは、このフィールドは空)
            - <code>pre_processing_rules</code> (array[object]) 前処理ルール
              - <code>id</code> (string) 前処理ルールの一意識別子
                - 列挙
                  - <code>remove_extra_spaces</code> 連続するスペース、改行、タブを置換
                  - <code>remove_urls_emails</code> URL、メールアドレスを削除
              - <code>enabled</code> (bool) このルールを選択するかどうか。ドキュメント ID が渡されない場合、デフォルト値を表します。
            - <code>segmentation</code> (object) セグメンテーションルール
              - <code>separator</code> カスタムセグメント識別子。現在は 1 つの区切り文字のみ設定可能。デフォルトは \n
              - <code>max_tokens</code> 最大長 (トークン) デフォルトは 1000
            - <code>parent_mode</code> 親チャンクの検索モード: <code>full-doc</code> 全文検索 / <code>paragraph</code> 段落検索
            - <code>subchunk_segmentation</code> (object) 子チャンクルール
              - <code>separator</code> セグメンテーション識別子。現在は 1 つの区切り文字のみ許可。デフォルトは <code>***</code>
              - <code>max_tokens</code> 最大長 (トークン) は親チャンクの長さより短いことを検証する必要があります
              - <code>chunk_overlap</code> 隣接するチャンク間の重複を定義 (オプション)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/update-by-text"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "name","text": "text"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-text' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "name",
        "text": "text"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "name.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/update-by-file'
  method='POST'
  title='ファイルでドキュメントを更新'
  name='#update-by-file'
/>
<Row>
  <Col>
    この API は既存のナレッジに基づいており、このナレッジを基にファイルを使用してドキュメントを更新します。

    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='name' type='string' key='name'>
        ドキュメント名 (オプション)
      </Property>
      <Property name='file' type='multipart/form-data' key='file'>
        アップロードするファイル
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        処理ルール
          - <code>mode</code> (string) クリーニング、セグメンテーションモード、自動 / カスタム
          - <code>rules</code> (object) カスタムルール (自動モードでは、このフィールドは空)
            - <code>pre_processing_rules</code> (array[object]) 前処理ルール
              - <code>id</code> (string) 前処理ルールの一意識別子
                - 列挙
                  - <code>remove_extra_spaces</code> 連続するスペース、改行、タブを置換
                  - <code>remove_urls_emails</code> URL、メールアドレスを削除
              - <code>enabled</code> (bool) このルールを選択するかどうか。ドキュメント ID が渡されない場合、デフォルト値を表します。
            - <code>segmentation</code> (object) セグメンテーションルール
              - <code>separator</code> カスタムセグメント識別子。現在は 1 つの区切り文字のみ設定可能。デフォルトは \n
              - <code>max_tokens</code> 最大長 (トークン) デフォルトは 1000
            - <code>parent_mode</code> 親チャンクの検索モード: <code>full-doc</code> 全文検索 / <code>paragraph</code> 段落検索
            - <code>subchunk_segmentation</code> (object) 子チャンクルール
              - <code>separator</code> セグメンテーション識別子。現在は 1 つの区切り文字のみ許可。デフォルトは <code>***</code>
              - <code>max_tokens</code> 最大長 (トークン) は親チャンクの長さより短いことを検証する必要があります
              - <code>chunk_overlap</code> 隣接するチャンク間の重複を定義 (オプション)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/update-by-file"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'data="{"name":"Dify","indexing_technique":"high_quality","process_rule":{"rules":{"pre_processing_rules":[{"id":"remove_extra_spaces","enabled":true},{"id":"remove_urls_emails","enabled":true}],"segmentation":{"separator":"###","max_tokens":500}},"mode":"custom"}}";type=text/plain' \\\n--form 'file=@"/path/to/file"'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-file' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'data="{\"name\":\"Dify\",\"indexing_technique\":\"high_quality\",\"process_rule\":{\"rules\":{\"pre_processing_rules\":[{\"id\":\"remove_extra_spaces\",\"enabled\":true},{\"id\":\"remove_urls_emails\",\"enabled\":true}],\"segmentation\":{\"separator\":\"###\",\"max_tokens\":500}},\"mode\":\"custom\"}}";type=text/plain' \
    --form 'file=@"/path/to/file"'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "Dify.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": "20230921150427533684"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{batch}/indexing-status'
  method='GET'
  title='ドキュメントの埋め込みステータス (進行状況) を取得'
  name='#indexing_status'
/>
<Row>
  <Col>
    ### パラメータ
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='batch' type='string' key='batch'>
        アップロードされたドキュメントのバッチ番号
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{batch}/indexing-status"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{batch}/indexing-status' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{batch}/indexing-status' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data":[{
        "id": "",
        "indexing_status": "indexing",
        "processing_started_at": 1681623462.0,
        "parsing_completed_at": 1681623462.0,
        "cleaning_completed_at": 1681623462.0,
        "splitting_completed_at": 1681623462.0,
        "completed_at": null,
        "paused_at": null,
        "error": null,
        "stopped_at": null,
        "completed_segments": 24,
        "total_segments": 100
      }]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}'
  method='DELETE'
  title='ドキュメントを削除'
  name='#delete_document'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents'
  method='GET'
  title='ナレッジベースのドキュメントリストを取得'
  name='#dataset_document_list'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>

    ### クエリ
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        検索キーワード、現在はドキュメント名のみ検索 (オプション)
      </Property>
      <Property name='page' type='string' key='page'>
        ページ番号 (オプション)
      </Property>
      <Property name='limit' type='string' key='limit'>
        返されるアイテム数、デフォルトは 20、範囲は 1-100 (オプション)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets/{dataset_id}/documents"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "",
          "position": 1,
          "data_source_type": "file_upload",
          "data_source_info": null,
          "dataset_process_rule_id": null,
          "name": "dify",
          "created_from": "",
          "created_by": "",
          "created_at": 1681623639,
          "tokens": 0,
          "indexing_status": "waiting",
          "error": null,
          "enabled": true,
          "disabled_at": null,
          "disabled_by": null,
          "archived": false
        },
      ],
      "has_more": false,
      "limit": 20,
      "total": 9,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments'
  method='POST'
  title='ドキュメントにチャンクを追加'
  name='#create_new_segment'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='segments' type='object list' key='segments'>
        - <code>content</code> (text) テキスト内容 / 質問内容、必須
        - <code>answer</code> (text) 回答内容、ナレッジのモードが Q&A モードの場合に値を渡します (オプション)
        - <code>keywords</code> (list) キーワード (オプション)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"segments": [{"content": "1","answer": "1","keywords": ["a"]}]}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "segments": [
        {
          "content": "1",
          "answer": "1",
          "keywords": ["a"]
        }
      ]
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
          "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments'
  method='GET'
  title='ドキュメントからチャンクを取得'
  name='#get_segment'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
    </Properties>

     ### クエリ
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        キーワード (オプション)
      </Property>
      <Property name='status' type='string' key='status'>
        検索ステータス、completed
      </Property>
      <Property name='page' type='string' key='page'>
        ページ番号 (オプション)
      </Property>
      <Property name='limit' type='string' key='limit'>
        返されるアイテム数、デフォルトは 20、範囲は 1-100 (オプション)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/segments"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model",
      "has_more": false,
      "limit": 20,
      "total": 9,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}'
  method='DELETE'
  title='ドキュメント内のチャンクを削除'
  name='#delete_segment'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        ドキュメントセグメント ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}'
  method='POST'
  title='ドキュメント内のチャンクを更新'
  name='#update_segment'
/>
<Row>
  <Col>
    ### POST
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        ドキュメントセグメント ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>content</code> (text) テキスト内容 / 質問内容、必須
        - <code>answer</code> (text) 回答内容、ナレッジが Q&A モードの場合に値を渡します (オプション)
        - <code>keywords</code> (list) キーワード (オプション)
        - <code>enabled</code> (bool) False / true (オプション)
        - <code>regenerate_child_chunks</code> (bool) 子チャンクを再生成するかどうか (オプション)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{\"segment\": {\"content\": \"1\",\"answer\": \"1\", \"keywords\": [\"a\"], \"enabled\": false}}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "segment": {
          "content": "1",
          "answer": "1",
          "keywords": ["a"],
          "enabled": false
      }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": {
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      },
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks'
  method='POST'
  title='子チャンクを作成'
  name='#create_child_chunk'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        セグメント ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='content' type='string' key='content'>
        子チャンクの内容
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"content": "Child chunk content"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "content": "Child chunk content"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": {
        "id": "",
        "segment_id": "",
        "content": "Child chunk content",
        "word_count": 25,
        "tokens": 0,
        "index_node_id": "",
        "index_node_hash": "",
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks'
  method='GET'
  title='子チャンクを取得'
  name='#get_child_chunks'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        セグメント ID
      </Property>
    </Properties>

    ### クエリ
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        検索キーワード (オプション)
      </Property>
      <Property name='page' type='integer' key='page'>
        ページ番号 (オプション、デフォルト: 1)
      </Property>
      <Property name='limit' type='integer' key='limit'>
        ページあたりのアイテム数 (オプション、デフォルト: 20、最大: 100)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks?page=1&limit=20' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "segment_id": "",
        "content": "Child chunk content",
        "word_count": 25,
        "tokens": 0,
        "index_node_id": "",
        "index_node_hash": "",
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "total": 1,
      "total_pages": 1,
      "page": 1,
      "limit": 20
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}'
  method='DELETE'
  title='子チャンクを削除'
  name='#delete_child_chunk'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        セグメント ID
      </Property>
      <Property name='child_chunk_id' type='string' key='child_chunk_id'>
        子チャンク ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}'
  method='PATCH'
  title='子チャンクを更新'
  name='#update_child_chunk'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        セグメント ID
      </Property>
      <Property name='child_chunk_id' type='string' key='child_chunk_id'>
        子チャンク ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='content' type='string' key='content'>
        子チャンクの内容
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="PATCH"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}"
      targetCode={`curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"content": "Updated child chunk content"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "content": "Updated child chunk content"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "data": {
        "id": "",
        "segment_id": "",
        "content": "Updated child chunk content",
        "word_count": 25,
        "tokens": 0,
        "index_node_id": "",
        "index_node_hash": "",
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/upload-file'
  method='GET'
  title='アップロードファイルを取得'
  name='#get_upload_file'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        ドキュメント ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/upload-file"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/upload-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/upload-file' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "id": "file_id",
      "name": "file_name",
      "size": 1024,
      "extension": "txt",
      "url": "preview_url",
      "download_url": "download_url",
      "mime_type": "text/plain",
      "created_by": "user_id",
      "created_at": 1728734540,
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/retrieve'
  method='POST'
  title='ナレッジベースからチャンクを取得'
  name='#dataset_retrieval'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='query' type='string' key='query'>
        クエリキーワード
      </Property>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        検索モデル (オプション、入力されない場合はデフォルトの方法でリコールされます)
        - <code>search_method</code> (text) 検索方法: 以下の 4 つのキーワードのいずれかが必要です
          - <code>keyword_search</code> キーワード検索
          - <code>semantic_search</code> セマンティック検索
          - <code>full_text_search</code> 全文検索
          - <code>hybrid_search</code> ハイブリッド検索
        - <code>reranking_enable</code> (bool) 再ランキングを有効にするかどうか、検索モードが semantic_search または hybrid_search の場合に必須 (オプション)
        - <code>reranking_mode</code> (object) 再ランキングモデル構成、再ランキングが有効な場合に必須
            - <code>reranking_provider_name</code> (string) 再ランキングモデルプロバイダー
            - <code>reranking_model_name</code> (string) 再ランキングモデル名
        - <code>weights</code> (float) ハイブリッド検索モードでのセマンティック検索の重み設定
        - <code>top_k</code> (integer) 返される結果の数 (オプション)
        - <code>score_threshold_enabled</code> (bool) スコア閾値を有効にするかどうか
        - <code>score_threshold</code> (float) スコア閾値
      </Property>
      <Property name='external_retrieval_model' type='object' key='external_retrieval_model'>
          未使用フィールド
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/retrieve"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/retrieve' \\\n--header 'Authorization: Bearer {api_key}'\\\n--header 'Content-Type: application/json'\\\n--data-raw '{
    "query": "test",
    "retrieval_model": {
        "search_method": "keyword_search",
        "reranking_enable": false,
        "reranking_mode": null,
        "reranking_model": {
            "reranking_provider_name": "",
            "reranking_model_name": ""
        },
        "weights": null,
        "top_k": 1,
        "score_threshold_enabled": false,
        "score_threshold": null
    }
}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/retrieve' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "query": "test",
        "retrieval_model": {
            "search_method": "keyword_search",
            "reranking_enable": false,
            "reranking_mode": null,
            "reranking_model": {
                "reranking_provider_name": "",
                "reranking_model_name": ""
            },
            "weights": null,
            "top_k": 2,
            "score_threshold_enabled": false,
            "score_threshold": null
        }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "query": {
        "content": "test"
      },
      "records": [
        {
          "segment": {
            "id": "7fa6f24f-8679-48b3-bc9d-bdf28d73f218",
            "position": 1,
            "document_id": "a8c6c36f-9f5d-4d7a-8472-f5d7b75d71d2",
            "content": "Operation guide",
            "answer": null,
            "word_count": 847,
            "tokens": 280,
            "keywords": [
              "install",
              "java",
              "base",
              "scripts",
              "jdk",
              "manual",
              "internal",
              "opens",
              "add",
              "vmoptions"
            ],
            "index_node_id": "39dd8443-d960-45a8-bb46-7275ad7fbc8e",
            "index_node_hash": "0189157697b3c6a418ccf8264a09699f25858975578f3467c76d6bfc94df1d73",
            "hit_count": 0,
            "enabled": true,
            "disabled_at": null,
            "disabled_by": null,
            "status": "completed",
            "created_by": "dbcb1ab5-90c8-41a7-8b78-73b235eb6f6f",
            "created_at": 1728734540,
            "indexing_at": 1728734552,
            "completed_at": 1728734584,
            "error": null,
            "stopped_at": null,
            "document": {
              "id": "a8c6c36f-9f5d-4d7a-8472-f5d7b75d71d2",
              "data_source_type": "upload_file",
              "name": "readme.txt",
            }
          },
          "score": 3.730463140527718e-05,
          "tsne_position": null
        }
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata'
  method='POST'
  title='ナレッジメタデータを作成'
  name='#create_metadata'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>type</code> (string) メタデータの種類、必須
        - <code>name</code> (string) メタデータの名前、必須
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/metadata"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/metadata' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{"type": "string", "name": "test"}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "id": "abc",
      "type": "string",
      "name": "test",
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata/{metadata_id}'
  method='PATCH'
  title='ナレッジメタデータを更新'
  name='#update_metadata'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='metadata_id' type='string' key='metadata_id'>
        メタデータ ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>name</code> (string) メタデータの名前、必須
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="PATCH"
      label="/datasets/{dataset_id}/metadata/{metadata_id}"
      targetCode={`curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}/metadata/{metadata_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{"name": "test"}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "id": "abc",
      "type": "string",
      "name": "test",
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata/{metadata_id}'
  method='DELETE'
  title='ナレッジメタデータを削除'
  name='#delete_metadata'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='metadata_id' type='string' key='metadata_id'>
        メタデータ ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="DELETE"
      label="/datasets/{dataset_id}/metadata/{metadata_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/metadata/{metadata_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata/built-in/{action}'
  method='POST'
  title='組み込みメタデータを無効化または有効化'
  name='#toggle_metadata'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
      <Property name='action' type='string' key='action'>
        disable/enable
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/metadata/built-in/{action}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/metadata/built-in/{action}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/metadata'
  method='POST'
  title='ドキュメントメタデータを更新'
  name='#update_documents_metadata'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>

    ### リクエストボディ
    <Properties>
      <Property name='operation_data' type='object list' key='segments'>
        - <code>document_id</code> (string) ドキュメント ID
        - <code>metadata_list</code> (list) メタデータリスト
          - <code>id</code> (string) メタデータ ID
          - <code>value</code> (string) メタデータの値
          - <code>name</code> (string) メタデータの名前
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="POST"
      label="/datasets/{dataset_id}/documents/metadata"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/metadata' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{"operation_data": [{"document_id": "document_id", "metadata_list": [{"id": "id", "value": "value", "name": "name"}]}]}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata'
  method='GET'
  title='ナレッジメタデータリストを取得'
  name='#dataset_metadata_list'
/>
<Row>
  <Col>
    ### パス
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        ナレッジ ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="リクエスト"
      tag="GET"
      label="/datasets/{dataset_id}/metadata"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/metadata' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
    <CodeGroup title="レスポンス">
    ```json {{ title: 'Response' }}
    {
      "doc_metadata": [
        {
          "id": "",
          "name": "name",
          "type": "string",
          "use_count": 0,
        },
        ...
      ],
      "built_in_field_enabled": true
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Row>
  <Col>
    ### エラーメッセージ
    <Properties>
      <Property name='code' type='string' key='code'>
        エラーコード
      </Property>
    </Properties>
    <Properties>
      <Property name='status' type='number' key='status'>
        エラーステータス
      </Property>
    </Properties>
    <Properties>
      <Property name='message' type='string' key='message'>
        エラーメッセージ
      </Property>
    </Properties>
  </Col>
  <Col>
    <CodeGroup title="例">
    ```json {{ title: 'Response' }}
      {
        "code": "no_file_uploaded",
        "message": "Please upload your file.",
        "status": 400
      }
    ```
    </CodeGroup>
  </Col>
</Row>
<table className="max-w-auto border-collapse border border-slate-400" style={{ maxWidth: 'none', width: 'auto' }}>
  <thead style={{ background: '#f9fafc' }}>
    <tr>
      <th className="p-2 border border-slate-300">code</th>
      <th className="p-2 border border-slate-300">status</th>
      <th className="p-2 border border-slate-300">message</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td className="p-2 border border-slate-300">no_file_uploaded</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Please upload your file.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">too_many_files</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Only one file is allowed.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">file_too_large</td>
      <td className="p-2 border border-slate-300">413</td>
      <td className="p-2 border border-slate-300">File size exceeded.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">unsupported_file_type</td>
      <td className="p-2 border border-slate-300">415</td>
      <td className="p-2 border border-slate-300">File type not allowed.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">high_quality_dataset_only</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Current operation only supports 'high-quality' datasets.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">dataset_not_initialized</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The dataset is still being initialized or indexing. Please wait a moment.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">archived_document_immutable</td>
      <td className="p-2 border border-slate-300">403</td>
      <td className="p-2 border border-slate-300">The archived document is not editable.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">dataset_name_duplicate</td>
      <td className="p-2 border border-slate-300">409</td>
      <td className="p-2 border border-slate-300">The dataset name already exists. Please modify your dataset name.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">invalid_action</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Invalid action.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">document_already_finished</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The document has been processed. Please refresh the page or go to the document details.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">document_indexing</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The document is being processed and cannot be edited.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">invalid_metadata</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The metadata content is incorrect. Please check and verify.</td>
    </tr>
  </tbody>
</table>
<div className="pb-4" />
