{/**
  * @typedef Props
  * @property {string} apiBaseUrl
  */}

import { CodeGroup } from '@/app/components/develop/code.tsx'
import { Row, Col, Properties, Property, Heading, SubProperty, PropertyInstruction, Paragraph } from '@/app/components/develop/md.tsx'

# Knowledge API

<div>
  ### Authentication

  Service API of Dify authenticates using an `API-Key`.

  It is suggested that developers store the `API-Key` in the backend instead of sharing or storing it in the client side to avoid the leakage of the `API-Key`, which may lead to property loss.

  All API requests should include your `API-Key` in the **`Authorization`** HTTP Header, as shown below:

  <CodeGroup title="Code">
    ```javascript
      Authorization: Bearer {API_KEY}

    ```
  </CodeGroup>
</div>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/document/create-by-text'
  method='POST'
  title='Create a Document from Text'
  name='#create-by-text'
/>
<Row>
  <Col>
    This API is based on an existing knowledge and creates a new document through text based on this knowledge.

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        Document name
      </Property>
      <Property name='text' type='string' key='text'>
        Document content
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        Index mode
          - <code>high_quality</code> High quality: embedding using embedding model, built as vector database index
          - <code>economy</code> Economy: Build using inverted index of keyword table index
      </Property>
      <Property name='doc_form' type='string' key='doc_form'>
        Format of indexed content
          - <code>text_model</code> Text documents are directly embedded; `economy` mode defaults to using this form
          - <code>hierarchical_model</code> Parent-child mode
          - <code>qa_model</code> Q&A Mode: Generates Q&A pairs for segmented documents and then embeds the questions
      </Property>
      <Property name='doc_language' type='string' key='doc_language'>
        In Q&A mode, specify the language of the document, for example: <code>English</code>, <code>Chinese</code>
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        Processing rules
          - <code>mode</code> (string) Cleaning, segmentation mode, automatic / custom
          - <code>rules</code> (object) Custom rules (in automatic mode, this field is empty)
            - <code>pre_processing_rules</code> (array[object]) Preprocessing rules
              - <code>id</code> (string) Unique identifier for the preprocessing rule
                - enumerate
                  - <code>remove_extra_spaces</code> Replace consecutive spaces, newlines, tabs
                  - <code>remove_urls_emails</code> Delete URL, email address
              - <code>enabled</code> (bool) Whether to select this rule or not. If no document ID is passed in, it represents the default value.
            - <code>segmentation</code> (object) Segmentation rules
              - <code>separator</code> Custom segment identifier, currently only allows one delimiter to be set. Default is \n
              - <code>max_tokens</code> Maximum length (token) defaults to 1000
            - <code>parent_mode</code> Retrieval mode of parent chunks: <code>full-doc</code> full text retrieval / <code>paragraph</code> paragraph retrieval
            - <code>subchunk_segmentation</code> (object) Child chunk rules
              - <code>separator</code> Segmentation identifier. Currently, only one delimiter is allowed. The default is <code>***</code>
              - <code>max_tokens</code> The maximum length (tokens) must be validated to be shorter than the length of the parent chunk
              - <code>chunk_overlap</code> Define the overlap between adjacent chunks (optional)
      </Property>
      <PropertyInstruction>When no parameters are set for the knowledge base, the first upload requires the following parameters to be provided; if not provided, the default parameters will be used.</PropertyInstruction>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        Retrieval model
          - <code>search_method</code> (string) Search method
            - <code>hybrid_search</code> Hybrid search
            - <code>semantic_search</code> Semantic search
            - <code>full_text_search</code> Full-text search
          - <code>reranking_enable</code> (bool) Whether to enable reranking
          - <code>reranking_mode</code> (object) Rerank model configuration
            - <code>reranking_provider_name</code> (string) Rerank model provider
            - <code>reranking_model_name</code> (string) Rerank model name
          - <code>top_k</code> (int) Number of results to return
          - <code>score_threshold_enabled</code> (bool) Whether to enable score threshold
          - <code>score_threshold</code> (float) Score threshold
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        Embedding model name
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        Embedding model provider
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/document/create-by-text"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "text","text": "text","indexing_technique": "high_quality","process_rule": {"mode": "automatic"}}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-text' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "text",
        "text": "text",
        "indexing_technique": "high_quality",
        "process_rule": {
            "mode": "automatic"
        }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
            "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "text.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695690280,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/document/create-by-file'
  method='POST'
  title='Create a Document from a File'
  name='#create-by-file'
/>
<Row>
  <Col>
    This API is based on an existing knowledge and creates a new document through a file based on this knowledge.

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='data' type='multipart/form-data json string' key='data'>
        - <code>original_document_id</code> Source document ID (optional)
          - Used to re-upload the document or modify the document cleaning and segmentation configuration. The missing information is copied from the source document
          - The source document cannot be an archived document
          - When original_document_id is passed in, the update operation is performed on behalf of the document. process_rule is a fillable item. If not filled in, the segmentation method of the source document will be used by default
          - When original_document_id is not passed in, the new operation is performed on behalf of the document, and process_rule is required

        - <code>indexing_technique</code> Index mode
          - <code>high_quality</code> High quality: embedding using embedding model, built as vector database index
          - <code>economy</code> Economy: Build using inverted index of keyword table index

        - <code>doc_form</code> Format of indexed content
          - <code>text_model</code> Text documents are directly embedded; `economy` mode defaults to using this form
          - <code>hierarchical_model</code> Parent-child mode
          - <code>qa_model</code> Q&A Mode: Generates Q&A pairs for segmented documents and then embeds the questions

        - <code>doc_language</code> In Q&A mode, specify the language of the document, for example: <code>English</code>, <code>Chinese</code>

        - <code>process_rule</code> Processing rules
          - <code>mode</code> (string) Cleaning, segmentation mode, automatic / custom
          - <code>rules</code> (object) Custom rules (in automatic mode, this field is empty)
            - <code>pre_processing_rules</code> (array[object]) Preprocessing rules
              - <code>id</code> (string) Unique identifier for the preprocessing rule
                - enumerate
                  - <code>remove_extra_spaces</code> Replace consecutive spaces, newlines, tabs
                  - <code>remove_urls_emails</code> Delete URL, email address
              - <code>enabled</code> (bool) Whether to select this rule or not. If no document ID is passed in, it represents the default value.
            - <code>segmentation</code> (object) Segmentation rules
              - <code>separator</code> Custom segment identifier, currently only allows one delimiter to be set. Default is \n
              - <code>max_tokens</code> Maximum length (token) defaults to 1000
            - <code>parent_mode</code> Retrieval mode of parent chunks: <code>full-doc</code> full text retrieval / <code>paragraph</code> paragraph retrieval
            - <code>subchunk_segmentation</code> (object) Child chunk rules
              - <code>separator</code> Segmentation identifier. Currently, only one delimiter is allowed. The default is <code>***</code>
              - <code>max_tokens</code> The maximum length (tokens) must be validated to be shorter than the length of the parent chunk
              - <code>chunk_overlap</code> Define the overlap between adjacent chunks (optional)
      </Property>
      <Property name='file' type='multipart/form-data' key='file'>
        Files that need to be uploaded.
      </Property>
      <PropertyInstruction>When no parameters are set for the knowledge base, the first upload requires the following parameters to be provided; if not provided, the default parameters will be used.</PropertyInstruction>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        Retrieval model
          - <code>search_method</code> (string) Search method
            - <code>hybrid_search</code> Hybrid search
            - <code>semantic_search</code> Semantic search
            - <code>full_text_search</code> Full-text search
          - <code>reranking_enable</code> (bool) Whether to enable reranking
          - <code>reranking_mode</code> (object) Rerank model configuration
            - <code>reranking_provider_name</code> (string) Rerank model provider
            - <code>reranking_model_name</code> (string) Rerank model name
          - <code>top_k</code> (int) Number of results to return
          - <code>score_threshold_enabled</code> (bool) Whether to enable score threshold
          - <code>score_threshold</code> (float) Score threshold
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        Embedding model name
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        Embedding model provider
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/document/create-by-file"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'data="{"indexing_technique":"high_quality","process_rule":{"rules":{"pre_processing_rules":[{"id":"remove_extra_spaces","enabled":true},{"id":"remove_urls_emails","enabled":true}],"segmentation":{"separator":"###","max_tokens":500}},"mode":"custom"}}";type=text/plain' \\\n--form 'file=@"/path/to/file"'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-file' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'data="{\"name\":\"Dify\",\"indexing_technique\":\"high_quality\",\"process_rule\":{\"rules\":{\"pre_processing_rules\":[{\"id\":\"remove_extra_spaces\",\"enabled\":true},{\"id\":\"remove_urls_emails\",\"enabled\":true}],\"segmentation\":{\"separator\":\"###\",\"max_tokens\":500}},\"mode\":\"custom\"}}";type=text/plain' \
    --form 'file=@"/path/to/file"'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "Dify.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets'
  method='POST'
  title='Create an Empty Knowledge Base'
  name='#create_empty_dataset'
/>
<Row>
  <Col>
    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        Knowledge name
      </Property>
      <Property name='description' type='string' key='description'>
        Knowledge description (optional)
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        Index technique (optional)
        If this is not set, embedding_model, embedding_provider_name and retrieval_model will be set to null
          - <code>high_quality</code> High quality
          - <code>economy</code> Economy
      </Property>
      <Property name='permission' type='string' key='permission'>
        Permission
          - <code>only_me</code> Only me
          - <code>all_team_members</code> All team members
          - <code>partial_members</code> Partial members
      </Property>
      <Property name='provider' type='string' key='provider'>
        Provider (optional, default: vendor)
          - <code>vendor</code> Vendor
          - <code>external</code> External knowledge
      </Property>
      <Property name='external_knowledge_api_id' type='str' key='external_knowledge_api_id'>
        External knowledge API ID (optional)
      </Property>
      <Property name='external_knowledge_id' type='str' key='external_knowledge_id'>
        External knowledge ID (optional)
      </Property>
      <Property name='embedding_model' type='str' key='embedding_model'>
        Embedding model name (optional)
      </Property>
      <Property name='embedding_provider_name' type='str' key='embedding_provider_name'>
        Embedding model provider name (optional)
      </Property>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        Retrieval model (optional)
          - <code>search_method</code> (string) Search method
            - <code>hybrid_search</code> Hybrid search
            - <code>semantic_search</code> Semantic search
            - <code>full_text_search</code> Full-text search
          - <code>reranking_enable</code> (bool) Whether to enable reranking
          - <code>reranking_model</code> (object) Rerank model configuration
              - <code>reranking_provider_name</code> (string) Rerank model provider
              - <code>reranking_model_name</code> (string) Rerank model name
          - <code>top_k</code> (int) Number of results to return
          - <code>score_threshold_enabled</code> (bool) Whether to enable score threshold
          - <code>score_threshold</code> (float) Score threshold
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "name", "permission": "only_me"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${apiBaseUrl}/v1/datasets' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "name": "name",
      "permission": "only_me"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "",
      "name": "name",
      "description": null,
      "provider": "vendor",
      "permission": "only_me",
      "data_source_type": null,
      "indexing_technique": null,
      "app_count": 0,
      "document_count": 0,
      "word_count": 0,
      "created_by": "",
      "created_at": **********,
      "updated_by": "",
      "updated_at": **********,
      "embedding_model": null,
      "embedding_model_provider": null,
      "embedding_available": null
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets'
  method='GET'
  title='Get Knowledge Base List'
  name='#dataset_list'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        Search keyword, optional
      </Property>
      <Property name='tag_ids' type='array[string]' key='tag_ids'>
        Tag ID list, optional
      </Property>
      <Property name='page' type='string' key='page'>
        Page number, optional, default 1
      </Property>
      <Property name='limit' type='string' key='limit'>
        Number of items returned, optional, default 20, range 1-100
      </Property>
      <Property name='include_all' type='boolean' key='include_all'>
        Whether to include all datasets (only effective for owners), optional, defaults to false
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets?page=1&limit=20' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "",
          "name": "name",
          "description": "desc",
          "permission": "only_me",
          "data_source_type": "upload_file",
          "indexing_technique": "",
          "app_count": 2,
          "document_count": 10,
          "word_count": 1200,
          "created_by": "",
          "created_at": "",
          "updated_by": "",
          "updated_at": ""
        },
        ...
      ],
      "has_more": true,
      "limit": 20,
      "total": 50,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}'
  method='GET'
  title='Get knowledge base details by knowledge base ID'
  name='#view_dataset'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge Base ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "eaedb485-95ac-4ffd-ab1e-18da6d676a2f",
      "name": "Test Knowledge Base",
      "description": "",
      "provider": "vendor",
      "permission": "only_me",
      "data_source_type": null,
      "indexing_technique": null,
      "app_count": 0,
      "document_count": 0,
      "word_count": 0,
      "created_by": "e99a1635-f725-4951-a99a-1daaaa76cfc6",
      "created_at": **********,
      "updated_by": "e99a1635-f725-4951-a99a-1daaaa76cfc6",
      "updated_at": **********,
      "embedding_model": null,
      "embedding_model_provider": null,
      "embedding_available": true,
      "retrieval_model_dict": {
        "search_method": "semantic_search",
        "reranking_enable": false,
        "reranking_mode": null,
        "reranking_model": {
          "reranking_provider_name": "",
          "reranking_model_name": ""
        },
        "weights": null,
        "top_k": 2,
        "score_threshold_enabled": false,
        "score_threshold": null
      },
      "tags": [],
      "doc_form": null,
      "external_knowledge_info": {
        "external_knowledge_id": null,
        "external_knowledge_api_id": null,
        "external_knowledge_api_name": null,
        "external_knowledge_api_endpoint": null
      },
      "external_retrieval_model": {
        "top_k": 2,
        "score_threshold": 0.0,
        "score_threshold_enabled": null
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}'
  method='PATCH'
  title='Update knowledge base'
  name='#update_dataset'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge Base ID
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        Index technique (optional)
          - <code>high_quality</code> High quality
          - <code>economy</code> Economy
      </Property>
      <Property name='permission' type='string' key='permission'>
        Permission
          - <code>only_me</code> Only me
          - <code>all_team_members</code> All team members
          - <code>partial_members</code> Partial members
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        Specified embedding model provider, must be set up in the system first, corresponding to the provider field(Optional)
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        Specified embedding model, corresponding to the model field(Optional)
      </Property>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        Retrieval model (optional, if not filled, it will be recalled according to the default method)
        - <code>search_method</code> (text) Search method: One of the following four keywords is required
          - <code>keyword_search</code> Keyword search
          - <code>semantic_search</code> Semantic search
          - <code>full_text_search</code> Full-text search
          - <code>hybrid_search</code> Hybrid search
        - <code>reranking_enable</code> (bool) Whether to enable reranking, required if the search mode is semantic_search or hybrid_search (optional)
        - <code>reranking_mode</code> (object) Rerank model configuration, required if reranking is enabled
            - <code>reranking_provider_name</code> (string) Rerank model provider
            - <code>reranking_model_name</code> (string) Rerank model name
        - <code>weights</code> (float) Semantic search weight setting in hybrid search mode
        - <code>top_k</code> (integer) Number of results to return (optional)
        - <code>score_threshold_enabled</code> (bool) Whether to enable score threshold
        - <code>score_threshold</code> (float) Score threshold
      </Property>
      <Property name='partial_member_list' type='array' key='partial_member_list'>
        Partial member list(Optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="PATCH"
      label="/datasets/{dataset_id}"
      targetCode={`curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{
        "name": "Test Knowledge Base", 
        "indexing_technique": "high_quality", 
        "permission": "only_me", 
        "embedding_model_provider": "zhipuai", 
        "embedding_model": "embedding-3", 
        "retrieval_model": {
          "search_method": "keyword_search",
          "reranking_enable": false,
          "reranking_mode": null,
          "reranking_model": {
              "reranking_provider_name": "",
              "reranking_model_name": ""
          },
          "weights": null,
          "top_k": 1,
          "score_threshold_enabled": false,
          "score_threshold": null
        }, 
        "partial_member_list": []
      }'
    `}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "name": "Test Knowledge Base", 
      "indexing_technique": "high_quality", 
      "permission": "only_me", 
      "embedding_model_provider": "zhipuai", 
      "embedding_model": "embedding-3", 
      "retrieval_model": {
        "search_method": "keyword_search",
        "reranking_enable": false,
        "reranking_mode": null,
        "reranking_model": {
            "reranking_provider_name": "",
            "reranking_model_name": ""
        },
        "weights": null,
        "top_k": 1,
        "score_threshold_enabled": false,
        "score_threshold": null
      }, 
      "partial_member_list": []
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "eaedb485-95ac-4ffd-ab1e-18da6d676a2f",
      "name": "Test Knowledge Base",
      "description": "",
      "provider": "vendor",
      "permission": "only_me",
      "data_source_type": null,
      "indexing_technique": "high_quality",
      "app_count": 0,
      "document_count": 0,
      "word_count": 0,
      "created_by": "e99a1635-f725-4951-a99a-1daaaa76cfc6",
      "created_at": **********,
      "updated_by": "e99a1635-f725-4951-a99a-1daaaa76cfc6",
      "updated_at": **********,
      "embedding_model": "embedding-3",
      "embedding_model_provider": "zhipuai",
      "embedding_available": null,
      "retrieval_model_dict": {
          "search_method": "semantic_search",
          "reranking_enable": false,
          "reranking_mode": null,
          "reranking_model": {
              "reranking_provider_name": "",
              "reranking_model_name": ""
          },
          "weights": null,
          "top_k": 2,
          "score_threshold_enabled": false,
          "score_threshold": null
      },
      "tags": [],
      "doc_form": null,
      "external_knowledge_info": {
          "external_knowledge_id": null,
          "external_knowledge_api_id": null,
          "external_knowledge_api_name": null,
          "external_knowledge_api_endpoint": null
      },
      "external_retrieval_model": {
          "top_k": 2,
          "score_threshold": 0.0,
          "score_threshold_enabled": null
      },
      "partial_member_list": []
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}'
  method='DELETE'
  title='Delete a Knowledge Base'
  name='#delete_dataset'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```text {{ title: 'Response' }}
    204 No Content
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/update-by-text'
  method='POST'
  title='Update a Document with Text'
  name='#update-by-text'
/>
<Row>
  <Col>
    This API is based on an existing knowledge and updates the document through text based on this knowledge.

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        Document name (optional)
      </Property>
      <Property name='text' type='string' key='text'>
        Document content (optional)
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        Processing rules
          - <code>mode</code> (string) Cleaning, segmentation mode, automatic / custom
          - <code>rules</code> (object) Custom rules (in automatic mode, this field is empty)
            - <code>pre_processing_rules</code> (array[object]) Preprocessing rules
              - <code>id</code> (string) Unique identifier for the preprocessing rule
                - enumerate
                  - <code>remove_extra_spaces</code> Replace consecutive spaces, newlines, tabs
                  - <code>remove_urls_emails</code> Delete URL, email address
              - <code>enabled</code> (bool) Whether to select this rule or not. If no document ID is passed in, it represents the default value.
            - <code>segmentation</code> (object) Segmentation rules
              - <code>separator</code> Custom segment identifier, currently only allows one delimiter to be set. Default is \n
              - <code>max_tokens</code> Maximum length (token) defaults to 1000
            - <code>parent_mode</code> Retrieval mode of parent chunks: <code>full-doc</code> full text retrieval / <code>paragraph</code> paragraph retrieval
            - <code>subchunk_segmentation</code> (object) Child chunk rules
              - <code>separator</code> Segmentation identifier. Currently, only one delimiter is allowed. The default is <code>***</code>
              - <code>max_tokens</code> The maximum length (tokens) must be validated to be shorter than the length of the parent chunk
              - <code>chunk_overlap</code> Define the overlap between adjacent chunks (optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/update-by-text"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "name","text": "text"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-text' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "name",
        "text": "text"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "name.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/update-by-file'
  method='POST'
  title='Update a Document with a File'
  name='#update-by-file'
/>
<Row>
  <Col>
    This API is based on an existing knowledge, and updates documents through files based on this knowledge

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        Document name (optional)
      </Property>
      <Property name='file' type='multipart/form-data' key='file'>
        Files to be uploaded
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        Processing rules
          - <code>mode</code> (string) Cleaning, segmentation mode, automatic / custom
          - <code>rules</code> (object) Custom rules (in automatic mode, this field is empty)
            - <code>pre_processing_rules</code> (array[object]) Preprocessing rules
              - <code>id</code> (string) Unique identifier for the preprocessing rule
                - enumerate
                  - <code>remove_extra_spaces</code> Replace consecutive spaces, newlines, tabs
                  - <code>remove_urls_emails</code> Delete URL, email address
              - <code>enabled</code> (bool) Whether to select this rule or not. If no document ID is passed in, it represents the default value.
            - <code>segmentation</code> (object) Segmentation rules
              - <code>separator</code> Custom segment identifier, currently only allows one delimiter to be set. Default is \n
              - <code>max_tokens</code> Maximum length (token) defaults to 1000
            - <code>parent_mode</code> Retrieval mode of parent chunks: <code>full-doc</code> full text retrieval / <code>paragraph</code> paragraph retrieval
            - <code>subchunk_segmentation</code> (object) Child chunk rules
              - <code>separator</code> Segmentation identifier. Currently, only one delimiter is allowed. The default is <code>***</code>
              - <code>max_tokens</code> The maximum length (tokens) must be validated to be shorter than the length of the parent chunk
              - <code>chunk_overlap</code> Define the overlap between adjacent chunks (optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/update-by-file"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'data="{"name":"Dify","indexing_technique":"high_quality","process_rule":{"rules":{"pre_processing_rules":[{"id":"remove_extra_spaces","enabled":true},{"id":"remove_urls_emails","enabled":true}],"segmentation":{"separator":"###","max_tokens":500}},"mode":"custom"}}";type=text/plain' \\\n--form 'file=@"/path/to/file"'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-file' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'data="{\"name\":\"Dify\",\"indexing_technique\":\"high_quality\",\"process_rule\":{\"rules\":{\"pre_processing_rules\":[{\"id\":\"remove_extra_spaces\",\"enabled\":true},{\"id\":\"remove_urls_emails\",\"enabled\":true}],\"segmentation\":{\"separator\":\"###\",\"max_tokens\":500}},\"mode\":\"custom\"}}";type=text/plain' \
    --form 'file=@"/path/to/file"'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "Dify.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": "20230921150427533684"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{batch}/indexing-status'
  method='GET'
  title='Get Document Embedding Status (Progress)'
  name='#indexing_status'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='batch' type='string' key='batch'>
        Batch number of uploaded documents
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{batch}/indexing-status"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{batch}/indexing-status' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{batch}/indexing-status' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data":[{
        "id": "",
        "indexing_status": "indexing",
        "processing_started_at": 1681623462.0,
        "parsing_completed_at": 1681623462.0,
        "cleaning_completed_at": 1681623462.0,
        "splitting_completed_at": 1681623462.0,
        "completed_at": null,
        "paused_at": null,
        "error": null,
        "stopped_at": null,
        "completed_segments": 24,
        "total_segments": 100
      }]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}'
  method='DELETE'
  title='Delete a Document'
  name='#delete_document'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents'
  method='GET'
  title='Get the Document List of a Knowledge Base'
  name='#dataset_document_list'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>

    ### Query
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        Search keywords, currently only search document names (optional)
      </Property>
      <Property name='page' type='string' key='page'>
        Page number (optional)
      </Property>
      <Property name='limit' type='string' key='limit'>
        Number of items returned, default 20, range 1-100 (optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "",
          "position": 1,
          "data_source_type": "file_upload",
          "data_source_info": null,
          "dataset_process_rule_id": null,
          "name": "dify",
          "created_from": "",
          "created_by": "",
          "created_at": 1681623639,
          "tokens": 0,
          "indexing_status": "waiting",
          "error": null,
          "enabled": true,
          "disabled_at": null,
          "disabled_by": null,
          "archived": false
        },
      ],
      "has_more": false,
      "limit": 20,
      "total": 9,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments'
  method='POST'
  title='Add Chunks to a Document'
  name='#create_new_segment'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='segments' type='object list' key='segments'>
        - <code>content</code> (text) Text content / question content, required
        - <code>answer</code> (text) Answer content, if the mode of the knowledge is Q&A mode, pass the value (optional)
        - <code>keywords</code> (list) Keywords (optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"segments": [{"content": "1","answer": "1","keywords": ["a"]}]}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "segments": [
        {
          "content": "1",
          "answer": "1",
          "keywords": ["a"]
        }
      ]
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
          "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments'
  method='GET'
  title='Get Chunks from a Document'
  name='#get_segment'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
    </Properties>

     ### Query
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        Keyword (optional)
      </Property>
      <Property name='status' type='string' key='status'>
        Search status, completed
      </Property>
      <Property name='page' type='string' key='page'>
        Page number (optional)
      </Property>
      <Property name='limit' type='string' key='limit'>
        Number of items returned, default 20, range 1-100 (optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/segments"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model",
      "has_more": false,
      "limit": 20,
      "total": 9,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}'
  method='DELETE'
  title='Delete a Chunk in a Document'
  name='#delete_segment'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        Document Segment ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}'
  method='POST'
  title='Update a Chunk in a Document'
  name='#update_segment'
/>
<Row>
  <Col>
    ### POST
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        Document Segment ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>content</code> (text) Text content / question content, required
        - <code>answer</code> (text) Answer content, passed if the knowledge is in Q&A mode (optional)
        - <code>keywords</code> (list) Keyword (optional)
        - <code>enabled</code> (bool) False / true (optional)
        - <code>regenerate_child_chunks</code> (bool) Whether to regenerate child chunks (optional)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{\"segment\": {\"content\": \"1\",\"answer\": \"1\", \"keywords\": [\"a\"], \"enabled\": false}}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "segment": {
          "content": "1",
          "answer": "1",
          "keywords": ["a"],
          "enabled": false
      }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": {
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      },
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks'
  method='POST'
  title='Create Child Chunk'
  name='#create_child_chunk'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        Segment ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='content' type='string' key='content'>
        Child chunk content
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"content": "Child chunk content"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "content": "Child chunk content"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": {
        "id": "",
        "segment_id": "",
        "content": "Child chunk content",
        "word_count": 25,
        "tokens": 0,
        "index_node_id": "",
        "index_node_hash": "",
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks'
  method='GET'
  title='Get Child Chunks'
  name='#get_child_chunks'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        Segment ID
      </Property>
    </Properties>

    ### Query
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        Search keyword (optional)
      </Property>
      <Property name='page' type='integer' key='page'>
        Page number (optional, default: 1)
      </Property>
      <Property name='limit' type='integer' key='limit'>
        Items per page (optional, default: 20, max: 100)
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks?page=1&limit=20' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "segment_id": "",
        "content": "Child chunk content",
        "word_count": 25,
        "tokens": 0,
        "index_node_id": "",
        "index_node_hash": "",
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "total": 1,
      "total_pages": 1,
      "page": 1,
      "limit": 20
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}'
  method='DELETE'
  title='Delete Child Chunk'
  name='#delete_child_chunk'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        Segment ID
      </Property>
      <Property name='child_chunk_id' type='string' key='child_chunk_id'>
        Child Chunk ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}'
  method='PATCH'
  title='Update Child Chunk'
  name='#update_child_chunk'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        Segment ID
      </Property>
      <Property name='child_chunk_id' type='string' key='child_chunk_id'>
        Child Chunk ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='content' type='string' key='content'>
        Child chunk content
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="PATCH"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}"
      targetCode={`curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"content": "Updated child chunk content"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}/child_chunks/{child_chunk_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "content": "Updated child chunk content"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": {
        "id": "",
        "segment_id": "",
        "content": "Updated child chunk content",
        "word_count": 25,
        "tokens": 0,
        "index_node_id": "",
        "index_node_hash": "",
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/upload-file'
  method='GET'
  title='Get Upload File'
  name='#get_upload_file'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        Document ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/upload-file"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/upload-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/upload-file' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "file_id",
      "name": "file_name",
      "size": 1024,
      "extension": "txt",
      "url": "preview_url",
      "download_url": "download_url",
      "mime_type": "text/plain",
      "created_by": "user_id",
      "created_at": 1728734540,
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/retrieve'
  method='POST'
  title='Retrieve Chunks from a Knowledge Base'
  name='#dataset_retrieval'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='query' type='string' key='query'>
        Query keyword
      </Property>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        Retrieval model (optional, if not filled, it will be recalled according to the default method)
        - <code>search_method</code> (text) Search method: One of the following four keywords is required
          - <code>keyword_search</code> Keyword search
          - <code>semantic_search</code> Semantic search
          - <code>full_text_search</code> Full-text search
          - <code>hybrid_search</code> Hybrid search
        - <code>reranking_enable</code> (bool) Whether to enable reranking, required if the search mode is semantic_search or hybrid_search (optional)
        - <code>reranking_mode</code> (object) Rerank model configuration, required if reranking is enabled
            - <code>reranking_provider_name</code> (string) Rerank model provider
            - <code>reranking_model_name</code> (string) Rerank model name
        - <code>weights</code> (float) Semantic search weight setting in hybrid search mode
        - <code>top_k</code> (integer) Number of results to return (optional)
        - <code>score_threshold_enabled</code> (bool) Whether to enable score threshold
        - <code>score_threshold</code> (float) Score threshold
      </Property>
      <Property name='external_retrieval_model' type='object' key='external_retrieval_model'>
          Unused field
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/retrieve"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/retrieve' \\\n--header 'Authorization: Bearer {api_key}'\\\n--header 'Content-Type: application/json'\\\n--data-raw '{
    "query": "test",
    "retrieval_model": {
        "search_method": "keyword_search",
        "reranking_enable": false,
        "reranking_mode": null,
        "reranking_model": {
            "reranking_provider_name": "",
            "reranking_model_name": ""
        },
        "weights": null,
        "top_k": 1,
        "score_threshold_enabled": false,
        "score_threshold": null
    }
}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/retrieve' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "query": "test",
        "retrieval_model": {
            "search_method": "keyword_search",
            "reranking_enable": false,
            "reranking_mode": null,
            "reranking_model": {
                "reranking_provider_name": "",
                "reranking_model_name": ""
            },
            "weights": null,
            "top_k": 2,
            "score_threshold_enabled": false,
            "score_threshold": null
        }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "query": {
        "content": "test"
      },
      "records": [
        {
          "segment": {
            "id": "7fa6f24f-8679-48b3-bc9d-bdf28d73f218",
            "position": 1,
            "document_id": "a8c6c36f-9f5d-4d7a-8472-f5d7b75d71d2",
            "content": "Operation guide",
            "answer": null,
            "word_count": 847,
            "tokens": 280,
            "keywords": [
              "install",
              "java",
              "base",
              "scripts",
              "jdk",
              "manual",
              "internal",
              "opens",
              "add",
              "vmoptions"
            ],
            "index_node_id": "39dd8443-d960-45a8-bb46-7275ad7fbc8e",
            "index_node_hash": "0189157697b3c6a418ccf8264a09699f25858975578f3467c76d6bfc94df1d73",
            "hit_count": 0,
            "enabled": true,
            "disabled_at": null,
            "disabled_by": null,
            "status": "completed",
            "created_by": "dbcb1ab5-90c8-41a7-8b78-73b235eb6f6f",
            "created_at": 1728734540,
            "indexing_at": 1728734552,
            "completed_at": 1728734584,
            "error": null,
            "stopped_at": null,
            "document": {
              "id": "a8c6c36f-9f5d-4d7a-8472-f5d7b75d71d2",
              "data_source_type": "upload_file",
              "name": "readme.txt",
            }
          },
          "score": 3.730463140527718e-05,
          "tsne_position": null
        }
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata'
  method='POST'
  title='Create a Knowledge Metadata'
  name='#create_metadata'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>type</code> (string) Metadata type, required
        - <code>name</code> (string) Metadata name, required
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/metadata"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/metadata' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{"type": "string", "name": "test"}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "abc",
      "type": "string",
      "name": "test",
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata/{metadata_id}'
  method='PATCH'
  title='Update a Knowledge Metadata'
  name='#update_metadata'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='metadata_id' type='string' key='metadata_id'>
        Metadata ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>name</code> (string) Metadata name, required
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="PATCH"
      label="/datasets/{dataset_id}/metadata/{metadata_id}"
      targetCode={`curl --location --request PATCH '${props.apiBaseUrl}/datasets/{dataset_id}/metadata/{metadata_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{"name": "test"}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "abc",
      "type": "string",
      "name": "test",
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata/{metadata_id}'
  method='DELETE'
  title='Delete a Knowledge Metadata'
  name='#delete_metadata'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='metadata_id' type='string' key='metadata_id'>
        Metadata ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}/metadata/{metadata_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/metadata/{metadata_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata/built-in/{action}'
  method='POST'
  title='Disable Or Enable Built-in Metadata'
  name='#toggle_metadata'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
      <Property name='action' type='string' key='action'>
        disable/enable
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/metadata/built-in/{action}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/metadata/built-in/{action}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/metadata'
  method='POST'
  title='Update Documents Metadata'
  name='#update_documents_metadata'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='operation_data' type='object list' key='segments'>
        - <code>document_id</code> (string) Document ID
        - <code>metadata_list</code> (list) Metadata list
          - <code>id</code> (string) Metadata ID
          - <code>value</code> (string) Metadata value
          - <code>name</code> (string) Metadata name
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/metadata"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/metadata' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{"operation_data": [{"document_id": "document_id", "metadata_list": [{"id": "id", "value": "value", "name": "name"}]}]}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/metadata'
  method='GET'
  title='Get Knowledge Metadata List'
  name='#dataset_metadata_list'
/>
<Row>
  <Col>
    ### Params
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        Knowledge ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/metadata"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/metadata' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "doc_metadata": [
        {
          "id": "",
          "name": "name",
          "type": "string",
          "use_count": 0,
        },
        ...
      ],
      "built_in_field_enabled": true
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />
 
<Heading
 url='/workspaces/current/models/model-types/text-embedding'
 method='GET'
 title='Get available embedding models'
 name='#model_type_list'
/>
<Row>
   <Col>
     ### Query
     <Properties>
     </Properties>
   </Col>
   <Col sticky>
     <CodeGroup
       title="Request"
       tag="GET"
       label="/datasets/{dataset_id}"
       targetCode={`curl --location --location --request GET '${props.apiBaseUrl}/workspaces/current/models/model-types/text-embedding' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' `}
     >
     ```bash {{ title: 'cURL' }}
     curl --location --request GET '${props.apiBaseUrl}/workspaces/current/models/model-types/text-embedding' \
     --header 'Authorization: Bearer {api_key}' \
     --header 'Content-Type: application/json' \
     ```
     </CodeGroup>
     <CodeGroup title="Response">
     ```json {{ title: 'Response' }}
     {
       "data": [
           {
               "provider": "zhipuai",
               "label": {
                   "zh_Hans": "智谱 AI",
                   "en_US": "ZHIPU AI"
               },
               "icon_small": {
                   "zh_Hans": "http://127.0.0.1:5001/console/api/workspaces/current/model-providers/zhipuai/icon_small/zh_Hans",
                   "en_US": "http://127.0.0.1:5001/console/api/workspaces/current/model-providers/zhipuai/icon_small/en_US"
               },
               "icon_large": {
                   "zh_Hans": "http://127.0.0.1:5001/console/api/workspaces/current/model-providers/zhipuai/icon_large/zh_Hans",
                   "en_US": "http://127.0.0.1:5001/console/api/workspaces/current/model-providers/zhipuai/icon_large/en_US"
               },
               "status": "active",
               "models": [
                   {
                       "model": "embedding-3",
                       "label": {
                           "zh_Hans": "embedding-3",
                           "en_US": "embedding-3"
                       },
                       "model_type": "text-embedding",
                       "features": null,
                       "fetch_from": "predefined-model",
                       "model_properties": {
                           "context_size": 8192
                       },
                       "deprecated": false,
                       "status": "active",
                       "load_balancing_enabled": false
                   },
                   {
                       "model": "embedding-2",
                       "label": {
                           "zh_Hans": "embedding-2",
                           "en_US": "embedding-2"
                       },
                       "model_type": "text-embedding",
                       "features": null,
                       "fetch_from": "predefined-model",
                       "model_properties": {
                           "context_size": 8192
                       },
                       "deprecated": false,
                       "status": "active",
                       "load_balancing_enabled": false
                   },
                   {
                       "model": "text_embedding",
                       "label": {
                           "zh_Hans": "text_embedding",
                           "en_US": "text_embedding"
                       },
                       "model_type": "text-embedding",
                       "features": null,
                       "fetch_from": "predefined-model",
                       "model_properties": {
                           "context_size": 512
                       },
                       "deprecated": false,
                       "status": "active",
                       "load_balancing_enabled": false
                   }
               ]
           }
       ]
     }
     ```
     </CodeGroup>
   </Col>
</Row>

<hr className='ml-0 mr-0' />

<Row>
  <Col>
    ### Error message
    <Properties>
      <Property name='code' type='string' key='code'>
        Error code
      </Property>
    </Properties>
    <Properties>
      <Property name='status' type='number' key='status'>
        Error status
      </Property>
    </Properties>
    <Properties>
      <Property name='message' type='string' key='message'>
        Error message
      </Property>
    </Properties>
  </Col>
  <Col>
    <CodeGroup title="Example">
    ```json {{ title: 'Response' }}
      {
        "code": "no_file_uploaded",
        "message": "Please upload your file.",
        "status": 400
      }
    ```
    </CodeGroup>
  </Col>
</Row>
<table className="max-w-auto border-collapse border border-slate-400" style={{ maxWidth: 'none', width: 'auto' }}>
  <thead style={{ background: '#f9fafc' }}>
    <tr>
      <th className="p-2 border border-slate-300">code</th>
      <th className="p-2 border border-slate-300">status</th>
      <th className="p-2 border border-slate-300">message</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td className="p-2 border border-slate-300">no_file_uploaded</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Please upload your file.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">too_many_files</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Only one file is allowed.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">file_too_large</td>
      <td className="p-2 border border-slate-300">413</td>
      <td className="p-2 border border-slate-300">File size exceeded.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">unsupported_file_type</td>
      <td className="p-2 border border-slate-300">415</td>
      <td className="p-2 border border-slate-300">File type not allowed.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">high_quality_dataset_only</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Current operation only supports 'high-quality' datasets.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">dataset_not_initialized</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The dataset is still being initialized or indexing. Please wait a moment.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">archived_document_immutable</td>
      <td className="p-2 border border-slate-300">403</td>
      <td className="p-2 border border-slate-300">The archived document is not editable.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">dataset_name_duplicate</td>
      <td className="p-2 border border-slate-300">409</td>
      <td className="p-2 border border-slate-300">The dataset name already exists. Please modify your dataset name.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">invalid_action</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Invalid action.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">document_already_finished</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The document has been processed. Please refresh the page or go to the document details.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">document_indexing</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The document is being processed and cannot be edited.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">invalid_metadata</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The metadata content is incorrect. Please check and verify.</td>
    </tr>
  </tbody>
</table>
<div className="pb-4" />
