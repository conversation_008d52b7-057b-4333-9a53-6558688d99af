model: cohere.command-r-16k
label:
  en_US: cohere.command-r-16k v1.2
model_type: llm
features:
  - multi-tool-call
  - agent-thought
  - stream-tool-call
model_properties:
  mode: chat
  context_size: 128000
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 1
    max: 1.0
  - name: topP
    use_template: top_p
    default: 0.75
    min: 0
    max: 1
  - name: topK
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
    default: 0
    min: 0
    max: 500
  - name: presencePenalty
    use_template: presence_penalty
    min: 0
    max: 1
    default: 0
  - name: frequencyPenalty
    use_template: frequency_penalty
    min: 0
    max: 1
    default: 0
  - name: maxTokens
    use_template: max_tokens
    default: 600
    max: 4000
pricing:
  input: '0.004'
  output: '0.004'
  unit: '0.0001'
  currency: USD
deprecated: true
