provider: idealab
label:
  en_US: ideaLab
icon_small:
  en_US: idealab_square.svg
icon_large:
  en_US: idealab.svg
background: "#F1EFED"
help:
  title:
    en_US: Get your API key from idealab
    zh_Hans: 从 idealab 获取 API Key
  url:
    en_US: https://aistudio.alibaba-inc.com/#/aistudio/manage/accessKeyManage
supported_model_types:
  - llm
  - text-embedding
  - speech2text
  - rerank
  - tts
configurate_methods:
  - predefined-model
  - customizable-model
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter full model name
      zh_Hans: 输入模型全称
  credential_form_schemas:
    - variable: api_key
      required: false
      label:
        en_US: API Key
      type: secret-input
      default: "YOUR-API-KEY-HERE"
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: mode
      show_on:
        - variable: __model_type
          value: llm
      label:
        en_US: Completion mode
      type: select
      required: false
      default: chat
      placeholder:
        zh_Hans: 选择对话类型
        en_US: Select completion mode
      options:
        - value: completion
          label:
            en_US: Completion
            zh_Hans: 补全
        - value: chat
          label:
            en_US: Chat
            zh_Hans: 对话
    - variable: context_size
      label:
        zh_Hans: 模型上下文长度
        en_US: Model context size
      required: true
      type: text-input
      default: "4096"
      placeholder:
        zh_Hans: 在此输入您的模型上下文长度
        en_US: Enter your Model context size
    - variable: max_tokens_to_sample
      label:
        zh_Hans: 最大 token 上限
        en_US: Upper bound for max tokens
      show_on:
        - variable: __model_type
          value: llm
      default: "4096"
      type: text-input
    - variable: vision_support
      show_on:
        - variable: __model_type
          value: llm
      label:
        zh_Hans: 是否支持 Vision
        en_US: Vision Support
      type: radio
      required: false
      default: "no_support"
      options:
        - value: "support"
          label:
            en_US: "Yes"
            zh_Hans: 是
        - value: "no_support"
          label:
            en_US: "No"
            zh_Hans: 否
provider_credential_schema:
  credential_form_schemas:
    - variable: api_key
      required: true
      label:
        en_US: API Key
      type: secret-input
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
