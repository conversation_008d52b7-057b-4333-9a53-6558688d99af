model: llama-3.2-11b-text-preview
deprecated: true
label:
  zh_<PERSON>: Llama 3.2 11B Text (Preview)
  en_US: Llama 3.2 11B Text (Preview)
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 131072
parameter_rules:
  - name: temperature
    use_template: temperature
  - name: top_p
    use_template: top_p
  - name: max_tokens
    use_template: max_tokens
    default: 512
    min: 1
    max: 8192
  - name: response_format
    label:
      zh_Hans: 回复格式
      en_US: Response Format
    type: string
    help:
      zh_Hans: 指定模型必须输出的格式
      en_US: specifying the format that the model must output
    required: false
    options:
      - text
      - json_object
pricing:
  input: '0.05'
  output: '0.1'
  unit: '0.000001'
  currency: USD
