model: llama2-70b-4096
label:
  zh_Hans: Llama-2-70B-4096
  en_US: Llama-2-70B-4096
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 4096
parameter_rules:
  - name: temperature
    use_template: temperature
  - name: top_p
    use_template: top_p
  - name: max_tokens
    use_template: max_tokens
    default: 512
    min: 1
    max: 4096
  - name: response_format
    label:
      zh_Hans: 回复格式
      en_US: Response Format
    type: string
    help:
      zh_Hans: 指定模型必须输出的格式
      en_US: specifying the format that the model must output
    required: false
    options:
      - text
      - json_object
pricing:
  input: '0.7'
  output: '0.8'
  unit: '0.000001'
  currency: USD
