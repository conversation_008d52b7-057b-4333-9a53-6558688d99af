model: qwen/qwen-2-7b-instruct
label:
  zh_<PERSON>: <PERSON>wen 2 7B Instruct
  en_US: Qwen 2 7B Instruct
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 32768
parameter_rules:
  - name: temperature
    use_template: temperature
    min: 0
    max: 2
    default: 1
  - name: top_p
    use_template: top_p
    min: 0
    max: 1
    default: 1
  - name: max_tokens
    use_template: max_tokens
    min: 1
    max: 2048
    default: 512
  - name: frequency_penalty
    use_template: frequency_penalty
    min: -2
    max: 2
    default: 0
  - name: presence_penalty
    use_template: presence_penalty
    min: -2
    max: 2
    default: 0
pricing:
  input: '0.00054'
  output: '0.00054'
  unit: '0.0001'
  currency: USD
