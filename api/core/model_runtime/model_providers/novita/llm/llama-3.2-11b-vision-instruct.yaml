model: meta-llama/llama-3.2-11b-vision-instruct
label:
  zh_<PERSON>: "Llama 3.2 11B Vision Instruct\t"
  en_US: "Llama 3.2 11B Vision Instruct\t"
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 32768
parameter_rules:
  - name: temperature
    use_template: temperature
    min: 0
    max: 2
    default: 1
  - name: top_p
    use_template: top_p
    min: 0
    max: 1
    default: 1
  - name: max_tokens
    use_template: max_tokens
    min: 1
    max: 2048
    default: 512
  - name: frequency_penalty
    use_template: frequency_penalty
    min: -2
    max: 2
    default: 0
  - name: presence_penalty
    use_template: presence_penalty
    min: -2
    max: 2
    default: 0
pricing:
  input: '0.0006'
  output: '0.0006'
  unit: '0.0001'
  currency: USD
