provider: yi
label:
  en_US: 01.AI
  zh_Hans: 零一万物
description:
  en_US: Models provided by 01.AI, such as yi-34b-chat and yi-vl-plus.
  zh_Hans: 零一万物提供的模型，例如 yi-34b-chat 和 yi-vl-plus。
icon_small:
  en_US: icon_s_en.svg
icon_large:
  en_US: icon_l_en.svg
background: "#E9F1EC"
help:
  title:
    en_US: Get your API Key from 01.ai
    zh_Hans: 从零一万物获取 API Key
  url:
    en_US: https://platform.lingyiwanwu.com/apikeys
supported_model_types:
  - llm
configurate_methods:
  - predefined-model
  - customizable-model
provider_credential_schema:
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: endpoint_url
      label:
        zh_Hans: 自定义 API endpoint 地址
        en_US: Custom API endpoint URL
      type: text-input
      required: false
      placeholder:
        zh_Hans: Base URL, e.g. https://api.lingyiwanwu.com/v1
        en_US: Base URL, e.g. https://api.lingyiwanwu.com/v1
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: context_size
      label:
        zh_Hans: 模型上下文长度
        en_US: Model context size
      required: true
      type: text-input
      default: '4096'
      placeholder:
        zh_Hans: 在此输入您的模型上下文长度
        en_US: Enter your Model context size
    - variable: max_tokens
      label:
        zh_Hans: 最大 token 上限
        en_US: Upper bound for max tokens
      default: '4096'
      type: text-input
      show_on:
        - variable: __model_type
          value: llm
    - variable: function_calling_type
      label:
        en_US: Function calling
      type: select
      required: false
      default: no_call
      options:
        - value: no_call
          label:
            en_US: Not Support
            zh_Hans: 不支持
        - value: function_call
          label:
            en_US: Support
            zh_Hans: 支持
      show_on:
        - variable: __model_type
          value: llm
