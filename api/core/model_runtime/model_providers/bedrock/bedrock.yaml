provider: bedrock
label:
  en_US: AWS
description:
  en_US: AWS Bedrock's models.
icon_small:
  en_US: icon_s_en.svg
icon_large:
  en_US: icon_l_en.svg
background: "#FCFDFF"
help:
  title:
    en_US: Get your Access Key and Secret Access Key from AWS Console
  url:
    en_US: https://console.aws.amazon.com/
supported_model_types:
  - llm
  - text-embedding
  - rerank
configurate_methods:
  - predefined-model
provider_credential_schema:
  credential_form_schemas:
    - variable: aws_access_key_id
      required: false
      label:
        en_US: Access Key (If not provided, credentials are obtained from the running environment.)
        zh_Hans: Access Key
      type: secret-input
      placeholder:
        en_US: Enter your Access Key
        zh_Hans: 在此输入您的 Access Key
    - variable: aws_secret_access_key
      required: false
      label:
        en_US: Secret Access Key
        zh_Hans: Secret Access Key
      type: secret-input
      placeholder:
        en_US: Enter your Secret Access Key
        zh_Hans: 在此输入您的 Secret Access Key
    - variable: aws_region
      required: true
      label:
        en_US: AWS Region
        zh_Hans: AWS 地区
        ja_JP: AWS リージョン
      type: select
      default: us-east-1
      options:
        - value: us-east-1
          label:
            en_US: US East (N. Virginia)
            zh_Hans: 美国东部 (弗吉尼亚北部)
            ja_JP: 米国 (バージニア北部)
        - value: us-east-2
          label:
            en_US: US East (Ohio)
            zh_Hans: 美国东部 (俄亥俄)
            ja_JP: 米国 (オハイオ)
        - value: us-west-2
          label:
            en_US: US West (Oregon)
            zh_Hans: 美国西部 (俄勒冈州)
            ja_JP: 米国 (オレゴン)
        - value: ap-south-1
          label:
            en_US: Asia Pacific (Mumbai)
            zh_Hans: 亚太地区（孟买）
            ja_JP: アジアパシフィック (ムンバイ)
        - value: ap-southeast-1
          label:
            en_US: Asia Pacific (Singapore)
            zh_Hans: 亚太地区 (新加坡)
            ja_JP: アジアパシフィック (シンガポール)
        - value: ap-southeast-2
          label:
            en_US: Asia Pacific (Sydney)
            zh_Hans: 亚太地区 (悉尼)
            ja_JP: アジアパシフィック (シドニー)
        - value: ap-northeast-1
          label:
            en_US: Asia Pacific (Tokyo)
            zh_Hans: 亚太地区 (东京)
            ja_JP: アジアパシフィック (東京)
        - value: ap-northeast-2
          label:
            en_US: Asia Pacific (Seoul)
            zh_Hans: 亚太地区（首尔）
            ja_JP: アジアパシフィック (ソウル)
        - value: ca-central-1
          label:
            en_US: Canada (Central)
            zh_Hans: 加拿大（中部）
            ja_JP: カナダ (中部)
        - value: eu-central-1
          label:
            en_US: Europe (Frankfurt)
            zh_Hans: 欧洲 (法兰克福)
            ja_JP: 欧州 (フランクフルト)
        - value: eu-west-1
          label:
            en_US: Europe (Ireland)
            zh_Hans: 欧洲（爱尔兰）
            ja_JP: 欧州 (アイルランド)
        - value: eu-west-2
          label:
            en_US: Europe (London)
            zh_Hans: 欧洲西部 (伦敦)
            ja_JP: 欧州 (ロンドン)
        - value: eu-west-3
          label:
            en_US: Europe (Paris)
            zh_Hans: 欧洲（巴黎）
            ja_JP: 欧州 (パリ)
        - value: sa-east-1
          label:
            en_US: South America (São Paulo)
            zh_Hans: 南美洲（圣保罗）
            ja_JP: 南米 (サンパウロ)
        - value: us-gov-west-1
          label:
            en_US: AWS GovCloud (US-West)
            zh_Hans: AWS GovCloud (US-West)
            ja_JP: AWS GovCloud (米国西部)
    - variable: bedrock_endpoint_url
      label:
        zh_Hans: Bedrock Endpoint URL
        en_US: Bedrock Endpoint URL
      type: text-input
      required: false
      placeholder:
        zh_Hans: 在此输入您的 Bedrock Endpoint URL, 如：https://123456.cloudfront.net
        en_US: Enter your Bedrock Endpoint URL, e.g. https://123456.cloudfront.net
    - variable: model_for_validation
      required: false
      label:
        en_US: Available Model Name
        zh_Hans: 可用模型名称
      type: text-input
      placeholder:
        en_US: A model you have access to (e.g. amazon.titan-text-lite-v1) for validation.
        zh_Hans: 为了进行验证，请输入一个您可用的模型名称 (例如：amazon.titan-text-lite-v1)
