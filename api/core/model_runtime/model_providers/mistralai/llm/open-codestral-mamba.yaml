model: open-codestral-mamba
label:
  zh_Hans: open-codestral-mamba
  en_US: open-codestral-mamba
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 256000
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 0.7
    min: 0
    max: 1
  - name: top_p
    use_template: top_p
    default: 1
    min: 0
    max: 1
  - name: max_tokens
    use_template: max_tokens
    default: 1024
    min: 1
    max: 16384
  - name: safe_prompt
    default: false
    type: boolean
    help:
      en_US: Whether to inject a safety prompt before all conversations.
      zh_Hans: 是否开启提示词审查
    label:
      en_US: SafePrompt
      zh_Hans: 提示词审查
  - name: random_seed
    type: int
    help:
      en_US: The seed to use for random sampling. If set, different calls will generate deterministic results.
      zh_Hans: 当开启随机数种子以后，你可以通过指定一个固定的种子来使得回答结果更加稳定
    label:
      en_US: RandomSeed
      zh_Hans: 随机数种子
    default: 0
    min: 0
    max: 2147483647
pricing:
  input: '0.008'
  output: '0.024'
  unit: '0.001'
  currency: USD
