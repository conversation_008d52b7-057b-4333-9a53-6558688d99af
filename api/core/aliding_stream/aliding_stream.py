#!/usr/bin/env python3
from __future__ import annotations

import asyncio
import logging
import threading
import time
from collections.abc import Callable
from enum import Str<PERSON><PERSON>
from typing import Any, Optional

import dingtalk_stream
from dingtalk_stream import AckMessage, DingTalkStreamClient, EventMessage

logger = logging.getLogger(__name__)


class StreamStatus(StrEnum):
    """
    Stream client status.
    """
    INIT = "init"
    RUNNING = "running"
    STOPPED = "stopped"


class AliDingStreamHandler(dingtalk_stream.EventHandler):
    """
    DingTalk stream event handler.
    """

    def __init__(self, stream_instance: AliDingStream):
        self.stream = stream_instance

    async def process(self, event: EventMessage):
        """
        Process DingTalk stream event.

        Args:
            event: Event message from DingTalk

        Returns:
            Tuple of status and message
        """
        # 广播消息给所有注册的回调
        for callback in self.stream.callbacks:
            try:
                callback(
                    event_type=event.headers.event_type,
                    event_id=event.headers.event_id,
                    data=event.data
                )
            except Exception as e:
                logger.exception("Error executing callback")
        return AckMessage.STATUS_OK, 'OK'


class AliDingStream:
    """
    DingTalk stream client singleton.
    """
    _instance: Optional[AliDingStream] = None
    _lock = threading.Lock()

    def __new__(cls) -> AliDingStream:
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.client: Optional[DingTalkStreamClient] = None
            self.status = StreamStatus.INIT
            self.client_id = 'dingmvr8tbrl2zrzdznj'  # TODO: Move to config
            self.client_secret = 'mFc5zGoAdX90QYHM4CzroOEJ3QK1dA5vp4ylKfNHr0g_lkcvG80M9fhS8PaGWTN-'  # TODO: Move to config
            self.callbacks: list[Callable] = []  # 回调函数列表
            self._stream_thread: Optional[threading.Thread] = None
            self._loop: Optional[asyncio.AbstractEventLoop] = None
            self.initialized = True

    def register_callback(self, callback: Callable[[str, str, dict[str, Any]], None]) -> None:
        """
        注册回调函数

        Args:
            callback: 回调函数,接收event_type, event_id和data作为参数
        """
        logger.info("Registering callback")
        self.callbacks.append(callback)

    def unregister_callback(self, callback: Callable) -> None:
        """
        注销回调函数

        Args:
            callback: 要注销的回调函数
        """
        logger.info("Unregistering callback")
        if callback in self.callbacks:
            self.callbacks.remove(callback)

    def _run_stream(self):
        """在新线程中运行事件循环"""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)

            def handle_exception(loop, context):
                exception = context.get('exception')
                msg = context.get('message', '')
                if isinstance(exception, asyncio.CancelledError):
                    return
                if 'Task was destroyed but it is pending' in msg:
                    # WebSocket 关闭时的正常现象
                    return
                logger.error(f"Unhandled exception in event loop: {context}")

            self._loop.set_exception_handler(handle_exception)

            try:
                credential = dingtalk_stream.Credential(self.client_id, self.client_secret)
                self.client = DingTalkStreamClient(credential)
                self.client.register_all_event_handler(AliDingStreamHandler(self))

                # 直接启动
                self._loop.run_until_complete(self.client.start())
                self._loop.run_forever()

            except asyncio.CancelledError:
                logger.info("Stream event loop was cancelled (expected during shutdown)")
            except Exception as e:
                logger.exception("Stream thread error")
                if "Event loop stopped before Future completed" in str(e):
                    logger.info("Stream shutdown completed (expected message)")
                else:
                    logger.exception("Stream thread error")
                    self.status = StreamStatus.STOPPED
            finally:
                self._cleanup_tasks()

        except Exception as e:
            logger.exception("Fatal error in stream thread")
            self.status = StreamStatus.STOPPED

    def _cleanup_tasks(self):
        """清理事件循环中的所有任务"""
        try:
            if not self._loop:
                return

            # 收集所有任务
            pending = asyncio.all_tasks(self._loop)
            if not pending:
                return

            # 取消所有任务
            for task in pending:
                task.cancel()

            # 等待所有任务完成
            try:
                # 使用 run_until_complete 而不是 wait_for，避免超时问题
                self._loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except Exception as e:
                if not isinstance(e, (asyncio.CancelledError, RuntimeError)):
                    logger.exception("Error during task cleanup")

        except Exception as e:
            logger.exception("Error in cleanup_tasks")
        finally:
            try:
                # 确保事件循环被关闭
                if self._loop and not self._loop.is_closed():
                    self._loop.close()
            except Exception as e:
                logger.exception("Error closing event loop")

    def start(self) -> None:
        """启动流客户端（非阻塞）"""
        if self.status == StreamStatus.RUNNING:
            logger.info("Stream client already running")
            return

        try:
            logger.info("Starting DingTalk stream client...")
            self.status = StreamStatus.RUNNING

            # 在新线程中启动事件循环
            self._stream_thread = threading.Thread(target=self._run_stream)
            self._stream_thread.daemon = True
            self._stream_thread.start()

            # 等待一小段时间确保启动成功
            time.sleep(0.5)

            if self.status == StreamStatus.RUNNING:
                logger.info("DingTalk stream client started successfully")
            else:
                raise RuntimeError("Failed to start stream client")

        except Exception as e:
            logger.exception("Failed to start stream client")
            self.status = StreamStatus.STOPPED
            raise

    def close(self) -> None:
        """关闭流客户端"""
        if self.status != StreamStatus.RUNNING:
            logger.info("Stream client not running")
            return

        try:
            logger.info("Closing DingTalk stream client...")

            # 先将状态设置为 STOPPED，防止新的操作
            self.status = StreamStatus.STOPPED

            # 停止事件循环
            if self._loop and self._loop.is_running():
                try:
                    # 停止事件循环
                    self._loop.call_soon_threadsafe(self._loop.stop)

                    # 等待线程结束
                    if self._stream_thread and self._stream_thread.is_alive():
                        self._stream_thread.join(timeout=3.0)

                except Exception as e:
                    logger.warning("Error during stream shutdown")
                finally:
                    # 确保进行清理
                    self._cleanup_tasks()

            logger.info("DingTalk stream client closed successfully")

        except Exception as e:
            logger.exception("Failed to close stream client")
        finally:
            # 清理资源
            self.client = None
            # 清空回调列表
            self.callbacks.clear()

    @property
    def is_running(self) -> bool:
        """
        Check if stream client is running.

        Returns:
            bool: True if running, False otherwise
        """
        return self.status == StreamStatus.RUNNING
