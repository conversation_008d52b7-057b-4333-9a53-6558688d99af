identity:
    name: moses_search_hot_note_data
    author: workflow
    label:
        en_US: search hot note
        zh_Hans: 找全网热帖
        pt_BR: search hot note
    icon: icon.svg
description:
    human:
        en_US: search hot note
        zh_Hans: 根据平台标识，检索热贴
        pt_BR: search hot note
    llm: moses
parameters:
    -   name: source
        type: select
        required: true
        label:
            en_US: source
            zh_Hans: 平台来源
            pt_BR: source
        options:
            -   value: eye
                label:
                    en_US: eye
                    zh_Hans: 微博
            -   value: apple
                label:
                    en_US: apple
                    zh_Hans: 抖音
            -   value: hat
                label:
                    en_US: hat
                    zh_Hans: 小红书
            -   value: foot
                label:
                    en_US: foot
                    zh_Hans: 百度
            -   value: bingo
                label:
                    en_US: bingo
                    zh_Hans: B站
            -   value: orange
                label:
                    en_US: orange
                    zh_Hans: 快手
            -   value: bean
                label:
                    en_US: bean
                    zh_Hans: 豆瓣
        human_description:
            en_US: source
            zh_Hans: 平台来源
            pt_BR: source
        form: form
    -   name: business_text_retrieval
        type: string
        required: true
        label:
            en_US: business_text_retrieval
            zh_Hans: 关键词
            pt_BR: business_text_retrieval
        human_description:
            en_US: business_text_retrieval
            zh_Hans: 检索关键词
            pt_BR: business_text_retrieval
        form: form
    -   name: pageNum
        type: number
        required: true
        label:
            en_US: pageNum
            zh_Hans: 页码
            pt_BR: pageNum
        human_description:
            en_US: pageNum
            zh_Hans: 页码
            pt_BR: pageNum
        form: form
    -   name: page_size
        type: number
        required: true
        label:
            en_US: page_size
            zh_Hans: 返回最大数量
            pt_BR: page_size
        human_description:
            en_US: page_size
            zh_Hans: 返回最大数量
            pt_BR: page_size
        form: form