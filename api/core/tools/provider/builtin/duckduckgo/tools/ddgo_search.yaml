identity:
  name: ddgo_search
  author: <PERSON><PERSON>
  label:
    en_US: DuckDuckGo Search
    zh_Hans: DuckDuckGo 搜索
description:
  human:
    en_US: Perform searches on DuckDuckGo and get results.
    zh_Hans: 在 DuckDuckGo 上进行搜索并获取结果。
  llm: Perform searches on DuckDuckGo and get results.
parameters:
  - name: query
    type: string
    required: true
    label:
      en_US: Query string
      zh_Hans: 查询语句
    human_description:
      en_US: The search query.
      zh_Hans: 搜索查询语句。
    llm_description: Key words for searching
    form: llm
  - name: max_results
    type: number
    required: true
    default: 5
    label:
      en_US: Max results
      zh_Hans: 最大结果数量
    form: form
  - name: require_summary
    type: boolean
    default: false
    label:
      en_US: Require Summary
      zh_Hans: 是否总结
    human_description:
      en_US: Whether to pass the search results to llm for summarization.
      zh_Hans: 是否需要将搜索结果传给大模型总结
    form: form
  - name: query_prefix
    label:
      en_US: Query Prefix
      zh_Hans: 查询前缀
    type: string
    required: false
    default: ""
    form: form
    human_description:
      en_US: Specific Search e.g. "site:wikipedia.org"
      zh_Hans: 定向搜索 e.g. "site:wikipedia.org"
