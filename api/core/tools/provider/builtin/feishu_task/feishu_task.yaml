identity:
  author: <PERSON> Lea
  name: feishu_task
  label:
    en_US: Feishu Task
    zh_Hans: 飞书任务
  description:
    en_US: |
      Feishu Task, requires the following permissions: task:task:write、contact:user.id:readonly.
    zh_Hans: |
      飞书任务，需要开通以下权限: task:task:write、contact:user.id:readonly。
  icon: icon.png
  tags:
    - social
    - productivity
credentials_for_provider:
  app_id:
    type: text-input
    required: true
    label:
      en_US: APP ID
    placeholder:
      en_US: Please input your feishu app id
      zh_Hans: 请输入你的飞书 app id
    help:
      en_US: Get your app_id and app_secret from <PERSON><PERSON><PERSON>
      zh_Hans: 从飞书获取您的 app_id 和 app_secret
    url: https://open.larkoffice.com/app
  app_secret:
    type: secret-input
    required: true
    label:
      en_US: APP Secret
    placeholder:
      en_US: Please input your app secret
      zh_Hans: 请输入你的飞书 app secret
