from typing import Optional

from pydantic import BaseModel, ConfigDict

from models.dataset import Document
from models.model import UploadFile


class AliDingInfo(BaseModel):
    """
    AliDing document info.
    """
    extension: Optional[str] = None
    union_id: Optional[str] = None
    dentry_uuid: Optional[str] = None
    format: Optional[str] = None
    name: Optional[str] = None
    content: Optional[str] = None
    url: Optional[str] = None
    task_id: Optional[str] = None
    category: Optional[str] = None
    create_time: Optional[str] = None
    creator_id: Optional[str] = None
    modified_time: Optional[str] = None
    modifier_id: Optional[str] = None
    workspace_id: Optional[str] = None
    tenant_id: Optional[str] = None
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **data) -> None:
        super().__init__(**data)


class YuqueInfo(BaseModel):
    """
    Yuque document info.
    """
    title: str
    body: str
    document: Document = None
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **data) -> None:
        super().__init__(**data)


class NotionInfo(BaseModel):
    """
    Notion import info.
    """

    notion_workspace_id: str
    notion_obj_id: str
    notion_page_type: str
    document: Optional[Document] = None
    tenant_id: str
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **data) -> None:
        super().__init__(**data)


class WebsiteInfo(BaseModel):
    """
    website import info.
    """

    provider: str
    job_id: str
    url: str
    mode: str
    tenant_id: str
    only_main_content: bool = False

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, **data) -> None:
        super().__init__(**data)


class ExtractSetting(BaseModel):
    """
    Model class for provider response.
    """

    datasource_type: str
    upload_file: Optional[UploadFile] = None
    notion_info: Optional[NotionInfo] = None
    website_info: Optional[WebsiteInfo] = None
    yuque_info: Optional[YuqueInfo] = None
    document_model: Optional[str] = None
    ali_ding_doc_info: Optional[AliDingInfo] = None
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **data) -> None:
        super().__init__(**data)
