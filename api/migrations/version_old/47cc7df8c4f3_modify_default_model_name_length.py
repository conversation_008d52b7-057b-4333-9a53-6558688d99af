"""modify default model name length

Revision ID: 47cc7df8c4f3
Revises: 3c7cac9521c6
Create Date: 2024-05-10 09:48:09.046298

"""
import sqlalchemy as sa
from alembic import op

import models as models

# revision identifiers, used by Alembic.
revision = '47cc7df8c4f3'
down_revision = '3c7cac9521c6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenant_default_models', schema=None) as batch_op:
        batch_op.alter_column('model_name',
                              existing_type=sa.VARCHAR(length=40),
                              type_=sa.String(length=255),
                              existing_nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenant_default_models', schema=None) as batch_op:
        batch_op.alter_column('model_name',
                              existing_type=sa.String(length=255),
                              type_=sa.VARCHAR(length=40),
                              existing_nullable=False)

    # ### end Alembic commands ###
