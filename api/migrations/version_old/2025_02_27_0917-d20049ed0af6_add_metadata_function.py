"""add_metadata_function

Revision ID: d20049ed0af6
Revises: 08ec4f75af5e
Create Date: 2025-02-27 09:17:48.903213

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd20049ed0af6'
down_revision = 'f051706725cc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dataset_metadata_bindings',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('dataset_id', models.types.StringUUID(), nullable=False),
    sa.Column('metadata_id', models.types.StringUUID(), nullable=False),
    sa.Column('document_id', models.types.StringUUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', models.types.StringUUID(), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_metadata_binding_pkey')
    )
    with op.batch_alter_table('dataset_metadata_bindings', schema=None) as batch_op:
        batch_op.create_index('dataset_metadata_binding_dataset_idx', ['dataset_id'], unique=False)
        batch_op.create_index('dataset_metadata_binding_document_idx', ['document_id'], unique=False)
        batch_op.create_index('dataset_metadata_binding_metadata_idx', ['metadata_id'], unique=False)
        batch_op.create_index('dataset_metadata_binding_tenant_idx', ['tenant_id'], unique=False)

    op.create_table('dataset_metadatas',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('dataset_id', models.types.StringUUID(), nullable=False),
    sa.Column('type', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('created_by', models.types.StringUUID(), nullable=False),
    sa.Column('updated_by', models.types.StringUUID(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='dataset_metadata_pkey')
    )
    with op.batch_alter_table('dataset_metadatas', schema=None) as batch_op:
        batch_op.create_index('dataset_metadata_dataset_idx', ['dataset_id'], unique=False)
        batch_op.create_index('dataset_metadata_tenant_idx', ['tenant_id'], unique=False)

    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.add_column(sa.Column('built_in_field_enabled', sa.Boolean(), server_default=sa.text('false'), nullable=False))

    with op.batch_alter_table('documents', schema=None) as batch_op:
        batch_op.alter_column('doc_metadata',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.create_index('document_metadata_idx', ['doc_metadata'], unique=False, postgresql_using='gin')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('documents', schema=None) as batch_op:
        batch_op.drop_index('document_metadata_idx', postgresql_using='gin')
        batch_op.alter_column('doc_metadata',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=postgresql.JSON(astext_type=sa.Text()),
               existing_nullable=True)

    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.drop_column('built_in_field_enabled')

    with op.batch_alter_table('dataset_metadatas', schema=None) as batch_op:
        batch_op.drop_index('dataset_metadata_tenant_idx')
        batch_op.drop_index('dataset_metadata_dataset_idx')

    op.drop_table('dataset_metadatas')
    with op.batch_alter_table('dataset_metadata_bindings', schema=None) as batch_op:
        batch_op.drop_index('dataset_metadata_binding_tenant_idx')
        batch_op.drop_index('dataset_metadata_binding_metadata_idx')
        batch_op.drop_index('dataset_metadata_binding_document_idx')
        batch_op.drop_index('dataset_metadata_binding_dataset_idx')

    op.drop_table('dataset_metadata_bindings')
    # ### end Alembic commands ###
