import logging
import time

from flask_restful import Resource, reqparse  # type: ignore
from werkzeug.exceptions import InternalServerError, NotFound

import services
from controllers.service_api import api
from controllers.service_api.app.error import (
    AppUnavailableError,
    CompletionRequestError,
    ConversationCompletedError,
    NotChatAppError,
    ProviderModelCurrentlyNotSupportError,
    ProviderNotInitializeError,
    ProviderQuotaExceededError,
)
from controllers.service_api.wraps import FetchUserArg, WhereisUserArg, validate_app_token
from controllers.web.error import InvokeRateLimitError as InvokeRateLimitHttpError
from core.app.apps.base_app_queue_manager import AppQueueManager
from core.app.entities.app_invoke_entities import InvokeFrom
from core.errors.error import (
    ModelCurrentlyNotSupportError,
    ProviderTokenNotInitError,
    QuotaExceededError,
)
from core.model_runtime.errors.invoke import InvokeError
from libs import helper
from libs.helper import uuid_value
from models.model import App, AppMode, EndUser
from services.app_generate_service import AppGenerateService
from services.errors.llm import InvokeRateLimitError
from utils import MetricsCounter, XFlushLogger
from services.errors.llm import InvokeRateLimitError


class CompletionApi(Resource):
    @validate_app_token(fetch_user_arg=FetchUserArg(fetch_from=WhereisUserArg.JSON, required=True))
    def post(self, app_model: App, end_user: EndUser):
        start_time = time.perf_counter()
        has_exception = True
        error_code = ""
        detail_msg = ""
        try:
            if app_model.mode != "completion":
                raise AppUnavailableError()

            parser = reqparse.RequestParser()
            parser.add_argument("inputs", type=dict, required=True, location="json")
            parser.add_argument("query", type=str, location="json", default="")
            parser.add_argument("files", type=list, required=False, location="json")
            parser.add_argument("response_mode", type=str, choices=["blocking", "streaming"], location="json")
            parser.add_argument("retriever_from", type=str, required=False, default="dev", location="json")

            args = parser.parse_args()

            streaming = args["response_mode"] == "streaming"

            args["auto_generate_name"] = False

            response = AppGenerateService.generate(
                app_model=app_model,
                user=end_user,
                args=args,
                invoke_from=InvokeFrom.SERVICE_API,
                streaming=streaming,
            )
            has_exception = False

            return helper.compact_generate_response(response)
        except services.errors.conversation.ConversationNotExistsError as e:
            error_code = "Conversation_Not_Exists"
            detail_msg = str(e)
            raise NotFound("Conversation Not Exists.")
        except services.errors.conversation.ConversationCompletedError as e:
            error_code = "Conversation_Completed_Error"
            detail_msg = str(e)
            raise ConversationCompletedError()
        except services.errors.app_model_config.AppModelConfigBrokenError as e:
            error_code = "App_Model_Config_Broken"
            detail_msg = str(e)
            logging.exception("App model config broken.")
            raise AppUnavailableError()
        except ProviderTokenNotInitError as ex:
            error_code = "Provider_Not_Initialize"
            detail_msg = str(ex)
            raise ProviderNotInitializeError(ex.description)
        except QuotaExceededError as e:
            detail_msg = str(e)
            error_code = "Quota_Exceeded"
            raise ProviderQuotaExceededError()
        except ModelCurrentlyNotSupportError as e:
            detail_msg = str(e)
            error_code = "Model_Currently_Not_Support"
            raise ProviderModelCurrentlyNotSupportError()
        except InvokeError as e:
            detail_msg = str(e)
            error_code = "invoke_error"
            raise CompletionRequestError(e.description)
        except ValueError as e:
            detail_msg = str(e)
            error_code = "value_error"
            raise e
        except Exception as e:
            detail_msg = str(e)
            error_code = "internal_server_error"
            logging.exception("internal server error.", str(e))
            raise InternalServerError()
        finally:
            end_time = time.perf_counter()
            cost_time = round((end_time - start_time) * 1000)
            app_info = f"{app_model.id}_{app_model.name}"
            XFlushLogger.log_yn_2("completionMessageRun", app_info,
                                0, 'N' if has_exception else 'Y',
                                cost_time, error_code, detail_msg)


class CompletionStopApi(Resource):
    @validate_app_token(fetch_user_arg=FetchUserArg(fetch_from=WhereisUserArg.JSON, required=True))
    def post(self, app_model: App, end_user: EndUser, task_id):
        if app_model.mode != "completion":
            raise AppUnavailableError()

        AppQueueManager.set_stop_flag(task_id, InvokeFrom.SERVICE_API, end_user.id)

        return {"result": "success"}, 200


class ChatApi(Resource):
    @validate_app_token(fetch_user_arg=FetchUserArg(fetch_from=WhereisUserArg.JSON, required=True))
    def post(self, app_model: App, end_user: EndUser):
        start_time = time.perf_counter()
        has_exception = True
        error_code = ""
        detail_msg = ""
        try:
            app_mode = AppMode.value_of(app_model.mode)
            if app_mode not in {AppMode.CHAT, AppMode.AGENT_CHAT, AppMode.ADVANCED_CHAT}:
                raise NotChatAppError()

            parser = reqparse.RequestParser()
            parser.add_argument("inputs", type=dict, required=True, location="json")
            parser.add_argument("query", type=str, required=True, location="json")
            parser.add_argument("files", type=list, required=False, location="json")
            parser.add_argument("response_mode", type=str, choices=["blocking", "streaming"], location="json")
            parser.add_argument("conversation_id", type=uuid_value, location="json")
            parser.add_argument("retriever_from", type=str, required=False, default="dev", location="json")
            parser.add_argument("auto_generate_name", type=bool, required=False, default=True, location="json")

            args = parser.parse_args()

            streaming = args["response_mode"] == "streaming"
            response = AppGenerateService.generate(
                app_model=app_model, user=end_user, args=args, invoke_from=InvokeFrom.SERVICE_API, streaming=streaming
            )
            has_exception = False
            return helper.compact_generate_response(response)
        except services.errors.conversation.ConversationNotExistsError as e:
            error_code = "Conversation_Not_Exists"
            detail_msg = str(e)
            raise NotFound("Conversation Not Exists.")
        except services.errors.conversation.ConversationCompletedError as e:
            error_code = "Conversation_Completed_Error"
            detail_msg = str(e)
            raise ConversationCompletedError()
        except services.errors.app_model_config.AppModelConfigBrokenError as e:
            error_code = "App_Model_Config_Broken"
            detail_msg = str(e)
            logging.exception("App model config broken.")
            raise AppUnavailableError()
        except ProviderTokenNotInitError as ex:
            error_code = "Provider_Not_Initialize"
            detail_msg = str(ex)
            raise ProviderNotInitializeError(ex.description)
        except QuotaExceededError as e:
            detail_msg = str(e)
            error_code = "Quota_Exceeded"
            raise ProviderQuotaExceededError()
        except ModelCurrentlyNotSupportError as e:
            detail_msg = str(e)
            error_code = "Model_Currently_Not_Support"
            raise ProviderModelCurrentlyNotSupportError()
        except InvokeRateLimitError as ex:
            error_code = "Invoke_Rate_Limit"
            detail_msg = str(ex)
            raise InvokeRateLimitHttpError(ex.description)
        except InvokeError as e:
            error_code = "invoke_error"
            detail_msg = str(e)
            raise CompletionRequestError(e.description)
        except ValueError as e:
            detail_msg = str(e)
            error_code = "value_error"
            raise e
        except Exception as e:
            error_code = "internal_server_error"
            detail_msg = str(e)
            logging.exception("internal server error.")
            raise InternalServerError()
        finally:
            end_time = time.perf_counter()
            if has_exception:
                counter = MetricsCounter.get_app_name_monitor_exception_counter()
                counter.add(1, {"appId": app_model.id})
            cost_time = round((end_time - start_time) * 1000)
            app_info = f"{app_model.id}_{app_model.name}"
            XFlushLogger.log_yn_2("chatMessageRun", app_info,
                                0, 'N' if has_exception else 'Y',
                                cost_time, error_code, detail_msg)


class ChatStopApi(Resource):
    @validate_app_token(fetch_user_arg=FetchUserArg(fetch_from=WhereisUserArg.JSON, required=True))
    def post(self, app_model: App, end_user: EndUser, task_id):
        app_mode = AppMode.value_of(app_model.mode)
        if app_mode not in {AppMode.CHAT, AppMode.AGENT_CHAT, AppMode.ADVANCED_CHAT}:
            raise NotChatAppError()

        AppQueueManager.set_stop_flag(task_id, InvokeFrom.SERVICE_API, end_user.id)

        return {"result": "success"}, 200


api.add_resource(CompletionApi, "/completion-messages")
api.add_resource(CompletionStopApi, "/completion-messages/<string:task_id>/stop")
api.add_resource(ChatApi, "/chat-messages")
api.add_resource(ChatStopApi, "/chat-messages/<string:task_id>/stop")
