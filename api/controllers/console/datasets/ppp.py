import requests
import json

tenant_id_list_only = ["b310c5fb-c91b-489e-b273-84bcdcde3063"]
print(tenant_id_list_only)
url = "https://pre-1d-workflow.alibaba-inc.com/console/api/workspaces/current/plugin/install/hand/pkg"
headers = {
    "Content-Type": "application/json",
    "Accept": "*/*",
    "Connection": "keep-alive",
    "Accept-Language": "zh-CN,zh;q=0.9"
}
plugin_unique_identifiers = [
    "langgenius/agent:0.0.13@8a9c70b0ed352e494af4f3817ba11a7526d62b1abccc63b71040be7a31e1e3df",
    "langgenius/deepseek:0.0.5@fd6efd37c2a931911de8ab9ca3ba2da303bef146d45ee87ad896b04b36d09403",
    "langgenius/idealab:0.0.1@1b44af0aab71cb5108248b243ddb560206be7ad02f8231ae2356b123fb4334c7",
    "langgenius/json_process:0.0.2@7afb534cc85b28a8e9c7f9410d1cfc31fb7bd3950023355a37059dbe809ac776",
    "langgenius/modelscope:0.0.4@5a3245e52b864fa8a20686baf5add2f5517eae5f11f94419bf46edbe170731e8",
    "langgenius/onedaybot:0.0.1@fa3fdb9cbc49051b9d79c7b9e7fa061f6dab95d7c95131587deb104ed38908c8",
    "langgenius/openai:0.0.22@fa668d0ec3b434270453ede311196acaad0531ad9e3d5561cd622e6508cd3254",
    "langgenius/openai_api_compatible:0.0.16@77274df8fe2632cac66bfd153fcc75aa5e96abbe92b5c611b8984ad9f4cd4457",
    "langgenius/openllm:0.0.2@4e5440508ce792e3d973e7e05a8172fe7ab5fbbfdfcfd7c26ab7299344853318",
    "langgenius/openrouter:0.0.8@c6508dfd6c5c6e7aa0c185df370b07fd093001df427d8e55b6d6f071fd30884b",
    "langgenius/tongyi:0.0.21@cb32d252bc1ebc61437c9134e22db2be5ccdd48223e08b9ea0deff4f0df0a187",
    "langgenius/whale:0.0.1@5b3f7dff74ffa8341e04c8d676b2cd8bd1d5591795a345900db86663815df84d",
    "langgenius/togetherai:0.0.2@93e8572b8180c0285ef630820931773c65fd54b1174d5e5fc6ea77b57dc93849",
    "kalochin/pdf_process:0.0.4@29c7b0927a11333044049de20657119779a06e0eb003b6c5cffaec07e5204332",
    "junjiem/mcp_sse:0.0.8@735533628518bba0c4690dc8c01e82f6a6b46ad121a8e44b1f33ccb21039a6a2",
    "jingfelix/bilibili_search:0.0.1@9a56ef972cebf7a5d097631797c22aa02b46864173b5dafe31e3d093d2149528",
    "arrenxxxxx/mcp_config_during_use:0.0.1@6639610b619840c14ef4612a018b465c29314d888adcfb08fdc562ea4a652ec3",
    "lework/prometheus:0.0.1@73c63eed6b4c672164072a6cb0ffc16d46df42bf933d7a315c6ca09ab9708aec",
    "liujicheng/aliyun_tool:0.0.1@cfac4caa162add082d9ef4a776486c21fa3818a5306604106acf87c57ef88873",
    "spance/db_client_node:0.1.47@f5c67e307064c72125fbdc5e3e51617d3041c97ae3587dc81f7bc60c71545f85",
    "quicksandzn/elasticsearch:0.0.5@5c97748cc033bb83b856fd5b53e145f5ab3da184aad37b8f8f6ba76ded0bfd2a",
    "zxr-alibaba/dingtalk_yida:0.0.1@7eb415756305b1468ddb35b0c1575e1b5a806c52229829888ee40feae4e3d994",
    "witmeng/ragflow-api:0.0.4@cbef498169ea866c57464c4a5cd7d73e69fa9450369a630ae8ac066e0c8d6972",
    "ssssshql/chat_memory_by_mongo:0.0.2@4fe290e75a091a7ced0c61f3835fc3ce53bd42ec3161940ac749b931f561d195"]
metas = {i: {} for i in range(len(plugin_unique_identifiers))}
print(f"metas:{metas}")
for tenant_id in tenant_id_list_only:
    for unique_identifier in plugin_unique_identifiers:
        data = json.dumps({
            "tenant_id": tenant_id,
            "plugin_unique_identifiers": [
                unique_identifier
            ], "metas": metas
        })
        response = requests.post(url, data=data, headers=headers)
        response.encoding = 'utf-8'
        print(f"tenant_id: {tenant_id}, response.text:{response.text}")

