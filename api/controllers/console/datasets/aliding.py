#!/usr/bin/env python3
from __future__ import annotations

import logging
import random
from threading import Event
from typing import Any

from alibabacloud_aliding20230426 import models as aliding_20230426_models
from alibabacloud_aliding20230426.client import Client as aliding20230426Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from flask import current_app, json
from flask_login import login_required
from flask_restful import Resource, reqparse
from opentelemetry import trace
from werkzeug.exceptions import BadRequest

from controllers.console import api
from controllers.console.wraps import account_initialization_required, setup_required
from core.aliding_stream import close_stream, get_stream_client, start_stream
from extensions.ext_redis import redis_client
from utils import MetricsCounter

logger = logging.getLogger(__name__)


def create_client() -> aliding20230426Client:
    """Create AliDing API client."""
    config = open_api_models.Config(
        access_key_id='NYusK2DqJH0dtODfUsOa',
        access_key_secret='_fSrK-7sIQayibwNGY5EN0OtWf51Al8JNZO44b8AhZjE7EBfIDfRZAS-9QcIKBiu'
    )
    config.endpoint = 'aliding.aliyuncs.com'
    return aliding20230426Client(config)


class JsonResponse(Resource):
    """Base resource with custom JSON encoder."""

    def make_response(self, data):
        return current_app.response_class(
            json.dumps(data, ensure_ascii=False),
            mimetype='application/json'
        )


class DirectoriesAPI(JsonResponse):
    """Get knowledge base directories."""

    def _convert_node_format(self, node: dict[str, Any]) -> dict[str, Any]:
        """转换节点格式，使其与原始格式保持一致"""
        return {
            "ContentType": node.get('Category', ''),
            "Creator": {
                "UserId": node.get('CreatorId', '')
            },
            "DentryType": "file" if node.get('Type') == 'FILE' else 'folder',
            "DentryUuid": node.get('NodeId', ''),
            "Extension": node.get('Extension', ''),
            "HasChildren": node.get('HasChildren', False),
            "Name": node.get('Name', ''),
            "SpaceId": node.get('WorkspaceId', ''),
            "Updater": {
                "UserId": node.get('ModifierId', '')
            },
            "Url": node.get('Url', ''),
            "children": []
        }

    def _get_child_nodes(self, client: aliding20230426Client, parent_node_id: str) -> list[dict[str, Any]]:
        """递归获取子节点列表"""
        try:
            account_context = aliding_20230426_models.ListNodesHeadersAccountContext(
                account_id='396756'
            )
            list_nodes_headers = aliding_20230426_models.ListNodesHeaders(
                account_context=account_context
            )
            list_nodes_request = aliding_20230426_models.ListNodesRequest(
                parent_node_id=parent_node_id
            )
            runtime = util_models.RuntimeOptions()

            response = client.list_nodes_with_options(list_nodes_request, list_nodes_headers, runtime)
            response_data = response.body.to_map()

            nodes = response_data.get('nodes', [])
            if not nodes:
                logger.warning(f"No nodes found for parent_node_id: {parent_node_id}")

            # 转换节点格式
            return [self._convert_node_format(node) for node in nodes]

        except Exception as error:
            logger.exception(f"Failed to get child nodes for {parent_node_id}")
            if hasattr(error, 'message'):
                logger.exception(f"Error message: {error.message}")
            if hasattr(error, 'data'):
                logger.exception(f"Error data: {error.data}")
            raise BadRequest(f"Failed to get child nodes: {str(error)}")

    def _process_directory_items(self, client: aliding20230426Client, items: list[dict[str, Any]]) -> list[
        dict[str, Any]]:
        """处理目录项，递归获取子节点"""
        for item in items:
            try:
                if item.get('HasChildren', False):
                    # 获取子节点
                    children = self._get_child_nodes(client, item.get('DentryUuid', ''))
                    # 递归处理子节点
                    item['children'] = self._process_directory_items(client, children)
                else:
                    item['children'] = []
            except Exception as e:
                logger.exception(f"Error processing item {item.get('Name')}")
                item['children'] = []
                item['error'] = str(e)
        return items

    @setup_required
    @login_required
    @account_initialization_required
    def post(self) -> dict[str, Any]:
        """Get knowledge base directories."""
        work_counter = MetricsCounter.get_ding_docs_get_directory_counter()
        work_counter.add(1, {"DingDocs": "getDirectories"})
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('space_id', type=str, required=True, location='json')
            args = parser.parse_args()

            logger.info("Getting knowledge base directories")
            client = create_client()

            # Prepare request
            account_context = aliding_20230426_models.GetSpaceDirectoriesHeadersAccountContext(
                account_id='396756'
            )
            get_space_directories_headers = aliding_20230426_models.GetSpaceDirectoriesHeaders(
                account_context=account_context
            )
            get_space_directories_request = aliding_20230426_models.GetSpaceDirectoriesRequest(
                space_id=args['space_id'],
                max_results=500
            )
            runtime = util_models.RuntimeOptions()

            # Execute request
            logger.info("Executing get_space_directories request")
            try:
                response = client.get_space_directories_with_options(get_space_directories_request,
                                                                     get_space_directories_headers, runtime)
                logger.info("Successfully retrieved directories")

                # 获取响应数据
                response_data = response.body.to_map()

                # 处理每个目录项
                if 'children' in response_data:
                    response_data['children'] = self._process_directory_items(client, response_data['children'])

            except Exception as error:
                logger.exception(f"Exception message: {error.message}")
                raise BadRequest(error.message)

            return self.make_response({
                'code': 200,
                'data': response_data
            })

        except Exception as e:
            logger.exception("Failed to get directories")
            error_response = {
                'code': 400,
                'message': str(e)
            }
            return self.make_response(error_response)


class CheckTaskIdsAPI(JsonResponse):
    """Get document content task ID."""

    # 该api默认会先启动stream 然后循环请求taskid 返回给用户一个list 里面是dist只包含dentry_uuids，task_id和success，success是请求是否成功
    @setup_required
    @login_required
    @account_initialization_required
    def post(self) -> dict[str, Any]:
        """Get document content task ID."""
        work_counter = MetricsCounter.ding_docs_check_task_counter
        work_counter.add(1, {"DingDocs": "checkTask"})
        tracer = trace.get_tracer(__name__)
        with tracer.start_as_current_span("check_task_span"):
            ctx = trace.get_current_span().get_span_context()
            trace_id = '{trace:032x}'.format(trace=ctx.trace_id)
            span_id = '{span:016x}'.format(span=ctx.span_id)
            logger.error(f"check_task trace_info traceId {trace_id} spanId {span_id}")
            try:
                parser = reqparse.RequestParser()
                parser.add_argument('dentry_uuids', type=list, required=True, location='json')
                args = parser.parse_args()

                logger.info(f"Getting task IDs for dentry_uuids: {args['dentry_uuids']}")
                client = create_client()

                # Prepare request
                account_context = aliding_20230426_models.GetDocContentTakIdHeadersAccountContext(
                    account_id='396756'
                )
                get_doc_content_tak_id_headers = aliding_20230426_models.GetDocContentTakIdHeaders(
                    account_context=account_context
                )
                runtime = util_models.RuntimeOptions()

                # Execute requests
                results = []
                logger.info("Executing get_doc_content_tak_id requests")

                for dentry_uuid in args['dentry_uuids']:
                    result = {
                        "DentryUuid": dentry_uuid,
                        "task_id": None,
                        "success": False,
                    }

                    try:
                        get_doc_content_tak_id_request = aliding_20230426_models.GetDocContentTakIdRequest(
                            dentry_uuid=dentry_uuid,
                            generate_cp=True
                        )
                        response = client.get_doc_content_tak_id_with_options(
                            get_doc_content_tak_id_request,
                            get_doc_content_tak_id_headers,
                            runtime
                        )
                        result.update({
                            "task_id": str(response.body.task_id),  # 转换为字符串
                            "success": True,
                        })
                    except Exception as error:
                        error_message = getattr(error, 'message', str(error))
                        logger.exception(f"Failed to get task ID for {dentry_uuid}: {error_message}")
                        result["error"] = error_message

                    results.append(result)

                logger.warning(f"Completed processing all dentry_uuids, task_ids: {results}")
                return self.make_response({
                    'code': 200,
                    'data': results
                })

            except Exception as e:
                logger.exception("Failed to process request")
                error_response = {
                    'code': 400,
                    'message': str(e)
                }
                return self.make_response(error_response)


class GetAliDingDocuments(JsonResponse):
    """获取钉钉文档内容"""

    def __init__(self):
        self.results = {}
        self.event = Event()
        self.app = None  # 应用上下文引用,将在post方法中初始化

    def handle_stream_message(self, event_type: str, event_id: str, data: dict[str, Any]) -> None:
        """处理钉钉消息回调"""
        logger.info(f"Received ding message - event_type: {event_type}, event_id: {event_id}")

        if event_type == "doc_content_export_result":
            task_id = str(data.get("taskId"))
            logger.info(f"Processing task_id: {task_id}")
            if task_id and task_id in self.results:
                content = data.get("content")
                logger.info(f"Updating content for task {task_id}")

                if not self.app:
                    logger.error("Application context not initialized")
                    return

                with self.app.app_context():
                    # 更新所有字段
                    self.results[task_id].update({
                        "eventId": data.get("eventId"),
                        "extension": data.get("extension"),
                        "unionId": data.get("unionId"),
                        "success": data.get("success", True),
                        "dentryUuid": data.get("dentryUuid"),
                        "format": data.get("format"),
                        "name": data.get("name"),
                        "type": data.get("type"),
                        "operation": data.get("operation"),
                        "content": content,
                        "url": data.get("url"),
                        "taskId": task_id,
                        "status": "completed"
                    })

                    # 检查所有任务是否完成
                    if all(r["status"] == "completed" for r in self.results.values()):
                        logger.info("All tasks completed, setting event")
                        self.event.set()
            else:
                logger.warning(f"Received message for unknown task_id: {task_id}")

    def double_get_task_id(self, tasks: list[dict[str, Any]]) -> None:
        """获取钉钉文档内容"""
        account_context = aliding_20230426_models.GetDocContentTakIdHeadersAccountContext(
            account_id='396756'
        )
        get_doc_content_tak_id_headers = aliding_20230426_models.GetDocContentTakIdHeaders(
            account_context=account_context
        )
        runtime = util_models.RuntimeOptions()
        client = create_client()
        for task in tasks:
            get_doc_content_tak_id_request = aliding_20230426_models.GetDocContentTakIdRequest(
                dentry_uuid=task["DentryUuid"],
                generate_cp=True
            )
            client.get_doc_content_tak_id_with_options(
                get_doc_content_tak_id_request,
                get_doc_content_tak_id_headers,
                runtime
            )

    def patch_get_node_basic_info(self, tasks: list[dict[str, Any]]) -> None:
        client = create_client()
        account_context = aliding_20230426_models.GetNodesHeadersAccountContext(
            account_id='396756'
        )
        get_nodes_headers = aliding_20230426_models.GetNodesHeaders(
            account_context=account_context
        )
        # 从tasks中提取所有的DentryUuid
        node_ids = [task["DentryUuid"] for task in tasks]
        if not node_ids:
            logger.warning("No DentryUuids found in tasks")
            return

        get_nodes_request = aliding_20230426_models.GetNodesRequest(
            node_ids=node_ids
        )
        runtime = util_models.RuntimeOptions()
        try:
            response = client.get_nodes_with_options(get_nodes_request, get_nodes_headers, runtime)
            response_data = response.body.to_map()

            # 更新节点信息到results中
            if 'nodes' in response_data:
                for node in response_data['nodes']:
                    # 通过NodeId找到对应的task_id
                    for task_id, result in self.results.items():
                        if result['dentryUuid'] == node['NodeId']:
                            # 更新节点信息
                            result.update({
                                "Category": node.get('Category'),
                                "CreateTime": node.get('CreateTime'),
                                "CreatorId": node.get('CreatorId'),
                                "HasChildren": node.get('HasChildren'),
                                "ModifiedTime": node.get('ModifiedTime'),
                                "ModifierId": node.get('ModifierId'),
                                "Size": node.get('Size'),
                                "StatisticalInfo": node.get('StatisticalInfo'),
                                "Type": node.get('Type'),
                                "WorkspaceId": node.get('WorkspaceId')
                            })
                            break
        except Exception as error:
            # 错误 message
            logger.exception(f"Failed to get nodes info: {error.message}")

    @setup_required
    @login_required
    @account_initialization_required
    def post(self) -> dict[str, Any]:
        """获取钉钉文档内容"""
        work_counter = MetricsCounter.get_ding_docs_get_document_counter()
        work_counter.add(1, {"DingDocs": "getDocument"})
        try:
            if redis_client.exists("stream_client_machine_lock"):
                raise ValueError("文档导入任务队列已满,请10秒后重试")
            # 保存应用上下文引用
            self.app = current_app._get_current_object()

            parser = reqparse.RequestParser()
            parser.add_argument('tasks', type=list, required=True, location='json')
            args = parser.parse_args()

            # 初始化结果集
            try:
                tasks = args['tasks']
                if not isinstance(tasks, list):
                    raise BadRequest("'tasks' must be a list")

                self.results = {}
                for task in tasks:
                    if not isinstance(task, dict):
                        raise BadRequest("Each task must be a dictionary")
                    if 'task_id' not in task or 'DentryUuid' not in task:
                        raise BadRequest("Each task must contain 'task_id' and 'DentryUuid'")

                    task_id = str(task["task_id"])  # 转换为字符串
                    self.results[task_id] = {
                        # 原有返回字段
                        "eventId": None,
                        "extension": None,
                        "unionId": None,
                        "success": None,
                        "dentryUuid": task["DentryUuid"],
                        "format": None,
                        "name": None,
                        "type": None,
                        "operation": None,
                        "content": None,
                        "url": None,
                        "taskId": task_id,
                        "status": "pending",
                        # 查询 node节点返回信息
                        "Category": None,
                        "CreateTime": None,
                        "CreatorId": None,
                        "HasChildren": None,
                        "ModifiedTime": None,
                        "ModifierId": None,
                        "Size": None,
                        "StatisticalInfo": None,
                        "Type": None,
                        "WorkspaceId": None
                    }

                if not self.results:
                    raise BadRequest("No valid tasks provided")
            except (KeyError, TypeError) as e:
                logger.exception("Error processing tasks")
                raise BadRequest(f"Invalid tasks format: {str(e)}")

            # 重置事件
            self.event = Event()

            # 获取全局 stream 客户端并注册回调
            stream_client = get_stream_client()
            stream_client.register_callback(self.handle_stream_message)

            try:
                # 启动 stream（会自动在20秒后关闭）
                start_stream()
                logger.warning(f"Started stream client for {len(tasks)} documents")

                # 再发一次taskId
                self.double_get_task_id(tasks)

                # 根据任务数量计算动态超时时间
                # 基础时间20秒，每个文件增加0.4秒，最大不超过100秒
                dynamic_timeout = min(20 + len(tasks) * 0.4, 100)
                logger.info(f"Setting dynamic timeout to {dynamic_timeout} seconds for {len(tasks)} tasks")

                # 等待结果或超时
                if not self.event.wait(timeout=dynamic_timeout):
                    logger.warning(f"Timeout waiting for {len(tasks)} documents after {dynamic_timeout} seconds")

                # 给节点添加基础信息
                self.patch_get_node_basic_info(tasks)

                return self.make_response({
                    'code': 200,
                    'data': list(self.results.values())
                })

            finally:
                # 注销回调函数
                stream_client.unregister_callback(self.handle_stream_message)
                close_stream()

        except Exception as e:
            logger.exception("Failed to get documents")
            raise BadRequest(str(e))


# Register resources
api.add_resource(DirectoriesAPI, "/datasets/aliding/directories")
api.add_resource(CheckTaskIdsAPI, "/datasets/aliding/check-tasks")
api.add_resource(GetAliDingDocuments, "/datasets/aliding/get-documents")
