# 用基础镜像地址替换下方镜像地址
FROM hub.docker.alibaba-inc.com/aone-base/oneday-workflow-api_base:20250317115536

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ARG APP_NAME
ENV APP_NAME=${APP_NAME}
ENV SSL_CERT_FILE=/etc/ssl/certs/ca-bundle.crt
ENV OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
ENV OTEL_PROPAGATORS=eagleeye
ENV OTEL_PYTHON_LOG_CORRELATION=true

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz
# 解压到/app/api来.
RUN cd /home/<USER>/${APP_NAME}/target/ && \
    tar xvf ${APP_NAME}.tgz && \
    cp -rv /home/<USER>/${APP_NAME}/target/${APP_NAME}/* /home/<USER>/${APP_NAME}/

# 解压nltk
RUN python -c "import nltk;import ssl;_create_unverified_https_context = ssl._create_unverified_context; ssl._create_default_https_context = _create_unverified_https_context;nltk.download('punkt');nltk.download('averaged_perceptron_tagger');nltk.download('averaged_perceptron_tagger_eng');"

COPY environment/py_vipserver-0.0.3-py3-none-any.whl  /opt/temp/py_vipserver-0.0.3-py3-none-any.whl
# 将应用nginx脚本复制到镜像中
COPY environment/common/cai/ /home/<USER>/cai/
RUN /usr/local/python3/bin/python3.12 -m pip install --upgrade pip -i http://artlab.alibaba-inc.com/1/pypi/py-simple && \
    pip install /opt/temp/py_vipserver-0.0.3-py3-none-any.whl
# 将应用启动脚本和配置文件复制到镜像中
COPY environment/common/bin/ /home/<USER>/${APP_NAME}/bin

# 设置文件操作权限
RUN chmod -R a+x /home/<USER>/${APP_NAME}/bin/ /home/<USER>/cai/bin/

# 加载依赖
COPY docker/pyproject.toml docker/poetry.lock /home/<USER>/
RUN poetry add /opt/temp/onnxruntime-1.20.1-cp312-cp312-manylinux_2_17_x86_64.whl  && \
    poetry install --no-root 2>&1

# ilogtail
RUN rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/stable/ali-sls-ilogtail/ali-sls-ilogtail-1.3.8-20240117121942.alios7.x86_64.rpm"

RUN echo "/home/<USER>/$APP_NAME/bin/appctl.sh stop" > /home/<USER>/stop.sh && \
echo "/home/<USER>/$APP_NAME/bin/appctl.sh restart" >> /home/<USER>/start.sh && \
echo "/home/<USER>/$APP_NAME/bin/preload.sh" > /home/<USER>/health.sh && \
chmod +x /home/<USER>/*.sh

# 设置logs账号信息
RUN mkdir -p /etc/ilogtail/
RUN echo  $APP_NAME-pre > /etc/ilogtail/user_defined_id && \
    touch /etc/ilogtail/users/1647796581073291

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/$APP_NAME/logs \
       /home/<USER>/logs \
       /home/<USER>/cai/logs \
       /home/<USER>/diamond \
       /home/<USER>/snapshots \
       /home/<USER>/configclient \
       /home/<USER>/notify \
       /home/<USER>/catserver \
       /home/<USER>/liaoyuan-out \
       /home/<USER>/vipsrv-dns \
       /home/<USER>/vipsrv-failover \
       /home/<USER>/vipsrv-cache \
       /home/<USER>/csp \
       /home/<USER>/.rocketmq_offsets \
       /home/<USER>/amsdata \
       /home/<USER>/amsdata_all

ENTRYPOINT ["/bin/bash", "/home/<USER>/start.sh"]

# Copy entrypoint
COPY docker/entrypoint.sh /home/<USER>/entrypoint.sh
# Copy ENV File
COPY .env_staging /home/<USER>/${APP_NAME}/.env

#ENTRYPOINT ["tail", "-f", "/dev/null"]
